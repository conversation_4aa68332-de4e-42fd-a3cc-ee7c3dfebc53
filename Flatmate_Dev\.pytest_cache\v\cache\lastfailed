{"flatmate/tests/Archived/test_module.py": true, "flatmate/tests/My_Test_File_Handling/Test_formatting.py": true, "flatmate/tests/My_Test_File_Handling/test_config.py": true, "flatmate/tests/test_data_pipeline.py": true, "flatmate/tests/test_format_detection.py": true, "flatmate/tests/test_format_detector.py": true, "flatmate/tests/test_home/test_home_presenter.py": true, "flatmate/tests/test_home/test_home_view.py": true, "flatmate/tests/test_home.py": true, "flatmate/tests/test_infobar.py": true, "flatmate/tests/test_test_data_manager.py": true, "flatmate/tests/test_ud_view.py": true, "flatmate/tests/test_ud_view_simple.py": true, "flatmate/tests/test_update_data.py": true, "flatmate/tests/test_view.py": true, "tests/test_real_csvs.py::test_handler": true, "flatmate/src/fm/core/data_services/standards/z_archive/Proposed_Enhanced_columns_system/test_enhanced_columns.py": true, "flatmate/src/fm/gui/_shared_components/table_view_v2/test_table_v2.py": true, "flatmate/src/fm/modules/update_data/utils/z_archive/run_pipeline_test.py": true, "flatmate/src/fm/modules/update_data/utils/z_archive/test_statement_validation.py": true, "flatmate/test_pipeline.py": true, "tests/test_dw_pipeline.py": true, "test_handler.py::test_handlers": true, "test_handler.py": true, "tests/test_real_csvs.py": true}