# Statement Import to Database Flow: Data Flow, Provenance & Validation Analysis

**Date:** 2025-01-14
**Status:** Code Review & Analysis
**Purpose:** Comprehensive analysis of the statement import pipeline with focus on data integrity, validation, and architectural issues

Prepared by ai - augment code -
dev comment s demarked by

>> ( text = dev coment )
>>

---

## Executive Summary

The statement import system has evolved into a complex multi-layered architecture with **significant validation and data integrity issues**. The current validation approach produces excessive false positives and lacks proper data provenance tracking. This analysis recommends a complete overhaul of the validation strategy and improved data lineage tracking.

>> this is accurate regards the post merge validation in pipeline, it's more trouble than its worth
>> I thnk  we should focus on duplicate detection -
>> any other error is from source (vanishingly unlikely and not our responsibility)
>>

- I think the de-dupulication logic is sound
- database does its own duplicate checking at each update
- at which point transactions are atomised and unique, source file and import date are about as good as we'll get re provenance
- adulterated imports are a result of user error

---

## 1. Data Flow Architecture

### 1.1 High-Level Flow

```
CSV Files → Statement Handlers → Pipeline Processing → Database Storage
    ↓              ↓                    ↓                    ↓
File Detection  Format Mapping    Validation/Merge    Duplicate Detection
```

### 1.2 Detailed Component Flow

#### **Stage 1: File Processing (`dw_director.py`)**

- **Input:** List of file paths from user selection
- **Process:**
  - Iterates through files
  - Uses handler registry to match file format
  - Delegates processing to specific statement handlers
- **Output:** List of formatted DataFrames
- **Issues:** No file provenance tracking, limited error context
  >> dev: we keep the source filenames
  >>
  >

#### **Stage 2: Statement Handlers (`statement_handlers/`)**

- **Input:** Raw CSV/XLSX files
- **Process:**
  - Bank-specific parsing logic
  - Column mapping to standard format
  - Account number extraction
  - Date format standardization
- **Output:** Standardized DataFrame with `Columns` schema
- **Issues:** Inconsistent validation across handlers
- >> handlers do not have specific validation this is done by the base statement handler banks have excellent validation so long as the reproduction is faithfull there is little concern
  >>
  >>
  >

#### **Stage 3: Pipeline Processing (`dw_pipeline.py`)**

- **Input:** Multiple formatted DataFrames
- **Process:**
  - DataFrame merging
  - Data validation (currently disabled due to false positives)
  - Final formatting
- **Output:** Single master DataFrame
- **Issues:** Validation disabled, no data lineage preservation
- >> The source filenames and file types should be recorded with each transaction >> currently we keep the source filneame we can eaily add file type
  >> eg kiwibank_basic_csv
  

#### **Stage 4: Database Import (`DBIOService` → `SQLiteTransactionRepository`)**

- **Input:** Master DataFrame
- **Process:**
  - Column validation
  - Unique ID generation (SHA1 hash)
  - Duplicate detection
  - Database insertion
- **Output:** `ImportResult` with counts
- **Issues:** Hash-based deduplication may miss legitimate duplicates
  >> there are no legitmate duplicates a duplicate (bank source colloms.Unique_ID is illegitmate
  >>
  >
- >> or transactions on the same day, with the same details and (if non zero) amount AND the same balance are duplicates. It is mathematically impoosible for them not to be
  >>
  >> - we could simplify the logic, a duplicate ()non NaN) Unique_ID with the same bank account number, is a duplicate, period
  >> - for others we compare the date, account, amount, details, and balance if all are the same - it's a duplicate.
  >>   we could leave out details even, it should work the same - except for 0 amount bank notifications
  >>
  >

---

## 2. Data Provenance Issues

### 2.1 Current Provenance Tracking

- **Source File:** Stored in `source_filename` column
- **Import Date:** Added during database insertion
- **Limitations:**
  - No tracking of processing steps
  - No validation history
  - No error context preservation
  - No handler identification
    >>  we should add file_type column back
    >> we could add statement handler version
    >> with a "handler" column
    >>
    >

### 2.2 Missing Provenance Elements

- Which handler processed each transaction
- Validation steps applied
- Data transformations performed
- Error/warning history
- Processing timestamps for each stage
- >>  this is potentially icated, how much meta data do we want ?
  >> we preserve all data found in original statements 
  >>
  >

---

## 3. Validation Analysis

### 3.1 Current Validation Layers

#### **Handler-Level Validation**

```python
# In _base_statement_handler.py
def _validate_data(self, df: pd.DataFrame) -> None:
    """Basic validation - mostly structural checks"""
```

- **Scope:** File format compliance
- **Issues:** Inconsistent across handlers, limited business logic

#### **Pipeline Validation (DISABLED)**

```python
# In dw_pipeline.py - Currently returns empty DataFrame
def _find_suspicious_transactions(df: pd.DataFrame) -> pd.DataFrame:
    """Validation disabled due to false positives"""
```

- **Original Intent:** Balance calculation validation
- **Current Status:** Completely disabled
- **Reason:** Excessive false positives from overlapping statements

#### **Database-Level Validation**

```python
# In db_io_service.py
if not (Columns.BALANCE in df.columns or Columns.UNIQUE_ID in df.columns):
    raise ValueError("DataFrame must contain either balance or unique_id column")
```

- **Scope:** Critical business rules
- **Issues:** Minimal validation, relies on upstream processing

### 3.2 Validation Problems

#### **False Positive Issues**

The balance validation was disabled because it couldn't handle:

- **Overlapping statement periods** from different CSV files
- **Out-of-order transactions** within statements
- **Different starting balances** between statement periods
- **Multiple account contexts** in merged data

#### **Missing Validation**

- **Date range validation** (future dates, unrealistic historical dates)
- **Amount validation** (extreme values, currency format issues)
- **Account number format validation**
- **Cross-file consistency checks**
- **Business rule validation** (e.g., balance progression within single statements)

---

## 4. Code Review Findings

### 4.1 Architecture Issues

#### **Tight Coupling**

```python
# dw_pipeline.py creates repository directly
repo = SQLiteTransactionRepository()
db_service = DBIOService(repo=repo)
```

- **Issue:** Hard-coded dependencies, difficult to test
- **Impact:** Poor testability, inflexible architecture

#### **Mixed Responsibilities**

- `dw_pipeline.py` handles both data processing AND database operations
- Statement handlers mix parsing logic with validation
- No clear separation between data transformation and persistence

#### **Error Handling**

```python
except Exception as e:
    log.error(f"Error adding transactions: {e}", exc_info=True)
    return ImportResult(0, 0, 0)
```

- **Issue:** Generic exception handling loses context
- **Impact:** Difficult debugging, poor user feedback

### 4.2 Data Integrity Issues

#### **Duplicate Detection Flaws**

```python
# SHA1 hash based on date, details, amount, account
def generate_unique_id(row):
    key_fields = [str(row.get(field, "")) for field in key_fields]
    return hashlib.sha1("".join(key_fields).encode()).hexdigest()
```

- **Issues:**
  - Minor formatting differences create different hashes
  - Legitimate duplicate transactions (same amount, same day) are merged
  - No handling of bank-provided unique IDs when available

#### **Data Type Inconsistencies**

- Date handling varies between handlers
- Amount formatting inconsistencies
- String encoding issues in hash generation

### 4.3 Performance Issues

- DataFrame operations not optimized for large datasets
- Multiple database connections created unnecessarily
- No batch processing for large imports

---

## 5. Recommendations

### 5.1 Immediate Fixes (High Priority)

#### **1. Implement Proper Data Provenance**

```python
@dataclass
class TransactionProvenance:
    source_file: str
    handler_used: str
    processing_timestamp: datetime
    validation_results: List[ValidationResult]
    transformations_applied: List[str]
```

#### **2. Redesign Validation Strategy**

- **File-level validation:** Within individual statement handlers
- **Cross-file validation:** Only for obvious data integrity issues
- **Configurable validation:** Allow users to enable/disable specific checks
- **Validation reporting:** Separate warnings from errors

#### **3. Improve Duplicate Detection**

```python

# Multi-tier duplicate detection
class DuplicateDetector:
    def detect_duplicates(self, df: pd.DataFrame) -> DuplicateResult:
        # 1. Use bank-provided unique IDs when available
        # 2. Fuzzy matching for similar transactions
        # 3. Hash-based detection as fallback
```

### 5.2 Architectural Improvements (Medium Priority)

#### **1. Dependency Injection**

```python
class DataPipeline:
    def __init__(self, db_service: DBIOService, validator: DataValidator):
        self.db_service = db_service
        self.validator = validator
```

#### **2. Separate Concerns**

- **Data Processing Layer:** Pure transformation functions
- **Validation Layer:** Configurable validation rules
- **Persistence Layer:** Database operations only
- **Orchestration Layer:** Workflow coordination

#### **3. Improved Error Handling**

```python
@dataclass
class ProcessingError:
    stage: str
    error_type: str
    message: str
    context: Dict[str, Any]
    recoverable: bool
```

### 5.3 Long-term Enhancements (Low Priority)

#### **1. Event-Driven Architecture**

- Processing events for monitoring
- Validation events for audit trails
- Error events for debugging

#### **2. Data Quality Metrics**

- Validation success rates
- Processing performance metrics
- Data completeness tracking

#### **3. Advanced Validation**

- Machine learning for anomaly detection
- Pattern recognition for fraud detection
- Automated data quality scoring

---

## 6. Conclusion

The current statement import system suffers from **over-engineered validation that produces false positives** and **under-engineered data provenance that hinders debugging**. The immediate priority should be:

1. **Disable problematic validation** (already done)
2. **Implement proper data provenance tracking**
3. **Redesign validation to be file-context-aware**
4. **Improve error handling and user feedback**

The system works functionally but needs significant refactoring to be maintainable and reliable for production use.

---

**Next Steps:**

1. Implement transaction provenance tracking
2. Create configurable validation framework
3. Improve duplicate detection algorithm
4. Add comprehensive error reporting
5. Refactor for better testability
