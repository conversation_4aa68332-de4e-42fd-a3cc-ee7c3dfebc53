#!/usr/bin/env python
"""
CLI Database Query Tool for Flatmate

This script provides a simple command-line interface to query the Flatmate database.
It allows listing transactions, filtering by date range, and viewing database statistics.
"""
import argparse
import sys
from datetime import datetime
from pathlib import Path

import pandas as pd
from PySide6.QtWidgets import QApplication, QFileDialog

# Add the src directory to the path so we can import the fm package
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from fm.core.services.logger import log, LogLevel
from fm.database_service.service import DataService

# Initialize data service
try:
    data_service = DataService()
    log.info("DataService initialized successfully")
except Exception as e:
    log.error(f"Error initializing DataService: {str(e)}")
    sys.exit(1)


def format_transaction(transaction):
    """Format a transaction for display."""
    return (
        f"Date: {transaction.date.strftime('%d-%m-%y')} | "
        f"Description: {transaction.description[:40]:40} | "
        f"Amount: {transaction.amount:10.2f} | "
        f"Account: {transaction.account_number}"
    )


def get_transactions(args):
    """Get transactions with optional date filtering.

    Args:
        args: Command line arguments

    Returns:
        List of Transaction objects
    """
    start_date = None
    end_date = None

    if hasattr(args, "start_date") and args.start_date:
        try:
            # Parse date in UK format (DD-MM-YY)
            start_date = datetime.strptime(args.start_date, "%d-%m-%y")
        except ValueError:
            log.error(f"Error: Invalid start date format. Use DD-MM-YY")
            return []

    if hasattr(args, "end_date") and args.end_date:
        try:
            # Parse date in UK format (DD-MM-YY)
            end_date = datetime.strptime(args.end_date, "%d-%m-%y")
        except ValueError:
            log.error(f"Error: Invalid end date format. Use DD-MM-YY")
            return []

    # Get transactions with optional date filtering
    if start_date and end_date:
        # Use date range filter
        transactions = data_service.get_transactions_by_date_range(start_date, end_date)
        date_info = f" from {args.start_date} to {args.end_date}"
    elif start_date:
        # For start date only, use current date as end date
        end = datetime.now()
        transactions = data_service.get_transactions_by_date_range(start_date, end)
        date_info = f" from {args.start_date}"
    elif end_date:
        # For end date only, use a very old date as start date
        start = datetime(1900, 1, 1)
        transactions = data_service.get_transactions_by_date_range(start, end_date)
        date_info = f" until {args.end_date}"
    else:
        # No date filtering
        transactions = data_service.get_transactions()
        date_info = ""

    # Sort transactions by date
    transactions.sort(key=lambda t: t.date if t.date else datetime.min)

    return transactions


def list_transactions(args):
    """List transactions with optional date filtering."""
    transactions = get_transactions(args)

    if not transactions:
        log.error("No transactions found.")
        return

    # Create date info string for display
    date_info = ""
    if (
        hasattr(args, "start_date")
        and hasattr(args, "end_date")
        and args.start_date
        and args.end_date
    ):
        date_info = f" from {args.start_date} to {args.end_date}"
    elif hasattr(args, "start_date") and args.start_date:
        date_info = f" from {args.start_date}"
    elif hasattr(args, "end_date") and args.end_date:
        date_info = f" until {args.end_date}"

    # Apply limit if specified
    if args.limit and args.limit > 0:
        display_transactions = transactions[: args.limit]
        limit_info = f" (showing first {args.limit})"
    else:
        display_transactions = transactions
        limit_info = ""

    # Display transactions
    print(f"Found {len(transactions)} transactions{date_info}{limit_info}:")
    print("-" * 100)

    for transaction in display_transactions:
        print(format_transaction(transaction))


def show_stats(args):
    """Show database statistics."""
    transactions = data_service.get_transactions()

    if not transactions:
        log.error("No transactions found in the database.")
        return

    # Calculate basic statistics
    total_count = len(transactions)

    # Get date range
    dates = [t.date for t in transactions]
    min_date = min(dates).strftime("%d-%m-%y")
    max_date = max(dates).strftime("%d-%m-%y")

    # Get account information
    accounts = {}
    for t in transactions:
        if t.account_number not in accounts:
            accounts[t.account_number] = {"count": 0, "total": 0}
        accounts[t.account_number]["count"] += 1
        accounts[t.account_number]["total"] += t.amount

    # Display statistics
    print(f"Database Statistics:")
    print(f"Total transactions: {total_count}")
    print(f"Date range: {min_date} to {max_date}")
    print(f"\nTransactions by account:")

    for account, data in accounts.items():
        print(
            f"  Account {account}: {data['count']} transactions, total amount: ${data['total']:.2f}"
        )


def export_to_csv(transactions, default_filename="fm_database_output.csv"):
    """Export transactions to CSV file with Qt save dialog.

    Args:
        transactions: List of Transaction objects
        default_filename: Default filename for save dialog

    Returns:
        Path to saved file or None if cancelled
    """
    log.info(f"Starting export of {len(transactions)} transactions...")
    if not transactions:
        log.error("No transactions to export")
        return None

    # Import StandardColumns to use standard column names
    import ast
    import json

    from fm.core.data_services.standards.fm_standard_columns import StandardColumns

    # Convert transactions to DataFrame with all available fields
    data = []
    for t in transactions:
        # Create a base dictionary with core fields using StandardColumns standard names
        transaction_dict = {
            # Core fields using StandardColumns standard names
            StandardColumns.DATE.value: t.date.strftime("%d-%m-%y") if t.date else "",
            StandardColumns.DETAILS.value: t.description,
            StandardColumns.AMOUNT.value: t.amount,
            StandardColumns.ACCOUNT.value: t.account_number,
            StandardColumns.PAYMENT_TYPE.value: t.transaction_type,
            StandardColumns.SOURCE_FILENAME.value: t.source_file,
            # Additional fields from Transaction model
            "Category": t.category,
            "Tags": t.tags,
            "Source Bank": t.source_bank,
            # Metadata fields
            "Import Date": t.import_date.strftime("%d-%m-%y") if t.import_date else "",
            "Modified Date": (
                t.modified_date.strftime("%d-%m-%y") if t.modified_date else ""
            ),
            "Transaction ID": t.transaction_id,
        }

        # Add transaction to data list
        data.append(transaction_dict)

    # Create DataFrame with all fields
    df = pd.DataFrame(data)
    log.info(f"Created DataFrame with {len(df)} rows and {len(df.columns)} columns")

    # Create Qt application for file dialog
    log.info("Initializing Qt application...")
    app = QApplication.instance()
    if not app:
        app = QApplication([])
        log.info("Created new Qt application")
    else:
        log.info("Using existing Qt application")

    # Create a dummy parent widget to ensure dialog appears properly
    from PySide6.QtWidgets import QWidget

    log.info("Creating dialog parent widget...")
    parent = QWidget()
    parent.resize(400, 200)  # Make it more visible
    parent.setWindowTitle("Save CSV Dialog")
    parent.show()
    parent.raise_()  # Bring to front
    parent.activateWindow()  # Activate the window

    # Show save dialog
    log.info("Opening file save dialog...")
    file_path, _ = QFileDialog.getSaveFileName(
        parent,
        "Save Transactions to CSV",
        str(Path.home() / default_filename),  # Use home directory for better visibility
        "CSV Files (*.csv);;All Files (*)",
    )
    log.info(
        f"Dialog result: {'File selected: ' + file_path if file_path else 'Cancelled'}"
    )

    # Clean up the parent widget
    parent.close()
    log.info("Closed parent widget")

    if file_path:
        # Ensure .csv extension
        if not file_path.lower().endswith(".csv"):
            file_path += ".csv"

        # Save to CSV
        df.to_csv(file_path, index=False)
        log.info(f"Exported {len(transactions)} transactions to {file_path}")
        return file_path
    else:
        log.info("Export cancelled")
        return None


def delete_all_transactions():
    """Delete all transactions from the database."""
    count = data_service.delete_all_transactions()
    log.info(f"Deleted {count} transactions from the database.")
    return count


def main():
    """Main entry point for the CLI tool."""
    parser = argparse.ArgumentParser(description="Flatmate Database Query Tool")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # List command
    list_parser = subparsers.add_parser("list", help="List transactions")
    list_parser.add_argument("--start-date", help="Start date (DD-MM-YY)")
    list_parser.add_argument("--end-date", help="End date (DD-MM-YY)")
    list_parser.add_argument("--limit", type=int, help="Limit number of transactions")

    # Delete all command
    subparsers.add_parser(
        "delete_all", help="Delete all transactions from the database"
    )
    list_parser.add_argument("--csv", action="store_true", help="Export results to CSV")

    # Stats command
    stats_parser = subparsers.add_parser("stats", help="Show database statistics")

    # Export command
    export_parser = subparsers.add_parser(
        "export", help="Export all transactions to CSV"
    )
    export_parser.add_argument("--start-date", help="Start date (DD-MM-YY)")
    export_parser.add_argument("--end-date", help="End date (DD-MM-YY)")

    args = parser.parse_args()

    if args.command == "list":
        list_transactions(args)
        # Export to CSV if requested
        if hasattr(args, "csv") and args.csv:
            transactions = get_transactions(args)
            if transactions:
                export_to_csv(transactions)
    elif args.command == "stats":
        show_stats(args)
    elif args.command == "export":
        # Export all transactions to CSV
        transactions = get_transactions(args)
        if transactions:
            export_to_csv(transactions)
        else:
            log.error("No transactions found to export.")
    elif args.command == "delete_all":
        # Delete all transactions from the database
        delete_all_transactions()
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log.error(f"Error: {str(e)}")
