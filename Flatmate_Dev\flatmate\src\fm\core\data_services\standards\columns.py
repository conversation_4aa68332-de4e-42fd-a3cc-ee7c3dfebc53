from typing import List, Any
from .column_definition import Column



class Columns:
    """
    A central, type-safe registry for all column definitions in the application.

    This class replaces the old StandardColumns enum and various column group lists.
    It provides a single source of truth for column metadata (db_name, display_name, dtype)
    and their logical groupings.

    Usage:
        - Get a specific column:      `Columns.DATE`
        - Get a column's db_name:    `Columns.DATE.db_name`
        - Get a group of columns:     `Columns.get(G_STATEMENT_HANDLER)`
    """
    # --- Core Transaction Identifiers ---
    DATE = Column(db_name='date', display_name='Date', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=12)
    DETAILS = Column(db_name='details', display_name='Details', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=40)
    AMOUNT = Column(db_name='amount', display_name='Amount', dtype=float, groups=['statement_handler', 'core_transaction_cols'], width=12)
    BALANCE = Column(db_name='balance', display_name='Balance', dtype=float, groups=['statement_handler', 'core_transaction_cols'], width=12)
    ACCOUNT = Column(db_name='account', display_name='Account', dtype=str, groups=['statement_handler', 'core_transaction_cols'], width=20)

    # --- Statement Handling Specific ---
    # Unique identification (separated for data integrity)
    SOURCE_UID = Column(db_name='source_uid', display_name='Source UID', dtype=str, groups=['statement_handler'])  # Bank-provided unique ID
    UNIQUE_ID = Column(db_name='unique_id', display_name='Unique Id', dtype=str, groups=['statement_handler'])  # Legacy - maps to SOURCE_UID
    EMPTY_COLUMN = Column(db_name='empty', display_name='Empty', dtype=Any, groups=['statement_handler'])
    CREDIT_AMOUNT = Column(db_name='credit_amount', display_name='Credit', dtype=float, groups=['statement_handler'])
    DEBIT_AMOUNT = Column(db_name='debit_amount', display_name='Debit', dtype=float, groups=['statement_handler'])
    PAYMENT_TYPE = Column(db_name='payment_type', display_name='Payment Type', dtype=str, groups=['statement_handler'])
    TP_REF = Column(db_name='tp_ref', display_name='TP Ref', dtype=str, groups=['statement_handler'])
    TP_PART = Column(db_name='tp_part', display_name='TP Part', dtype=str, groups=['statement_handler'])
    TP_CODE = Column(db_name='tp_code', display_name='TP Code', dtype=str, groups=['statement_handler'])
    OP_REF = Column(db_name='op_ref', display_name='OP Ref', dtype=str, groups=['statement_handler'])
    OP_PART = Column(db_name='op_part', display_name='OP Part', dtype=str, groups=['statement_handler'])
    OP_CODE = Column(db_name='op_code', display_name='OP Code', dtype=str, groups=['statement_handler'])
    OP_NAME = Column(db_name='op_name', display_name='OP Name', dtype=str, groups=['statement_handler'])
    OP_ACCOUNT = Column(db_name='op_account', display_name='OP Account', dtype=str, groups=['statement_handler'])

    ## ---Statement Handler MetaData ---
    SOURCE_FILE = Column(db_name='source_filename', display_name='Source Filename', dtype=str, groups=['statement_handler'])
    SOURCE_BANK = Column(db_name='source_bank', display_name='Source Bank', dtype=str, groups=['statement_handler'])
    SOURCE_TYPE = Column(db_name='source_type', display_name='Statement Type', dtype=str, groups=['statement_handler'])
    STATEMENT_DATE = Column(db_name='statement_date', display_name='Statement Date', dtype=str, groups=['statement_handler'])

    # --- User-Editable Fields ---
    CATEGORY = Column(db_name='category', display_name='Category', dtype=str, groups=['user_editable'], width=20)
    TAGS = Column(db_name='tags', display_name='Tags', dtype=str, groups=['user_editable'], width=30)
    NOTES = Column(db_name='notes', display_name='Notes', dtype=str, groups=['user_editable'], width=40)

    # ---Database System Columns (not for default display or direct user editing) ---can be displayed when selected
    IMPORT_DATE = Column(db_name='import_date', display_name='Import Date', dtype=str, groups=['db_system'])
    MODIFIED_DATE = Column(db_name='modified_date', display_name='Modified Date', dtype=str, groups=['db_system'])
    DB_UID = Column(db_name='db_uid', display_name='DB UID', dtype=str, groups=['db_system'])  # Generated hash for duplicate detection
    HASH = Column(db_name='hash', display_name='Hash', dtype=str, groups=['db_system'])  # Legacy - use DB_UID instead

    # --- Internal Methods for Registry Access ---
    @classmethod
    def get_all_columns(cls) -> List[Column]:
        """Returns a list of all Column objects defined in this class."""
        return [getattr(cls, attr) for attr in dir(cls) if isinstance(getattr(cls, attr), Column)]

    @classmethod
    def get_transaction_columns(cls) -> List[Column]:
        """Returns all columns needed for the Transaction table, including db_system columns."""
        # Transaction table needs ALL columns - statement data, user data, and system data
        return cls.get_all_columns()

    @classmethod
    def get(cls, group_name: str) -> List[Column]:
        """Returns a list of all columns belonging to the specified group."""
        return [col for col in cls.get_all_columns() if group_name in col.groups]

    @classmethod
    def from_db_name(cls, db_name: str):
        """Returns a Column object from its database name."""
        for col in cls.get_all_columns():
            if col.db_name == db_name:
                return col
        return None

    @classmethod
    def from_display_name(cls, display_name: str):
        """Returns a Column object from its display name."""
        for col in cls.get_all_columns():
            if col.display_name == display_name:
                return col
        return None

    # --- CONSOLIDATED METHODS (from ColumnManager and ColumnNameService) ---

    @classmethod
    def get_display_columns(cls) -> List[Column]:
        """Returns columns for UI display (core_transaction + user_editable)."""
        core = cls.get('core_transaction')
        user = cls.get('user_editable')
        return list(dict.fromkeys(core + user))

    @classmethod
    def get_display_mapping(cls, db_names: List[str]) -> dict[str, str]:
        """Returns a db_name -> display_name mapping for the given db_names."""
        mapping = {}
        for name in db_names:
            col = cls.from_db_name(name)
            if col:
                mapping[name] = col.display_name
            else:
                mapping[name] = name.replace('_', ' ').title()  # Fallback
        return mapping

    @classmethod
    def get_reverse_display_mapping(cls, display_names: List[str]) -> dict[str, str]:
        """Returns a display_name -> db_name mapping for the given display_names."""
        mapping = {}
        for name in display_names:
            col = cls.from_display_name(name)
            if col:
                mapping[name] = col.db_name
            else:
                mapping[name] = name.replace(' ', '_').lower() # Fallback
        return mapping

    @classmethod
    def apply_display_names_to_df(cls, df):
        """Converts a DataFrame's column names from db_names to display_names."""
        if df.empty:
            return df
        mapping = cls.get_display_mapping(df.columns.tolist())
        return df.rename(columns=mapping)

    @classmethod
    def get_column_widths(cls) -> dict[str, int]:
        """Returns a mapping of display_name -> width for all columns."""
        return {col.display_name: col.width for col in cls.get_all_columns()}

    @classmethod
    def get_default_visible_columns(cls, module_name: str) -> List[str]:
        """Returns a list of default visible column db_names for a given module."""
        if module_name == 'categorize':
            return [
                cls.DATE.db_name,
                cls.DETAILS.db_name,
                cls.AMOUNT.db_name,
                cls.ACCOUNT.db_name,
                cls.TAGS.db_name,
                cls.CATEGORY.db_name,
            ]
        # Fallback for other modules or default case
        return [
            cls.DATE.db_name,
            cls.DETAILS.db_name,
            cls.AMOUNT.db_name,
            cls.ACCOUNT.db_name,
        ]
