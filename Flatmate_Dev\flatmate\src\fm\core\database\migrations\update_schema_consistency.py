#!/usr/bin/env python3
"""
Database migration script to update column names for consistency with StandardColumns.

This script:
1. Renames columns in the transactions table to match the StandardColumns enum
2. Updates any views or indices that reference the old column names
"""
import os
import sqlite3
import logging
from fm.core.data_services.standards.fm_standard_columns import StandardColumns

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database path
DB_PATH = os.path.expanduser("~/.flatmate/data/transactions.db")

def get_column_mappings():
    """Generate column mappings from StandardColumns with fallback for legacy names"""
    # Map legacy column names to their StandardColumns.db_name equivalents
    legacy_mappings = {
        'description': StandardColumns.DETAILS.db_name,
        'account_number': StandardColumns.ACCOUNT.db_name,
        'source_file': StandardColumns.SOURCE_FILENAME.db_name,
        'op_account_number': 'op_account'  # Only if not in StandardColumns
    }
    
    # Add all StandardColumns with their db_name
    standard_mappings = {col.db_name: col.db_name for col in StandardColumns}
    
    # Combine with legacy mappings (legacy takes precedence for backward compatibility)
    return {**standard_mappings, **legacy_mappings}

# Get the column mappings
COLUMN_MAPPINGS = get_column_mappings()

def backup_database(db_path):
    """Create a backup of the database before making changes"""
    import shutil
    backup_path = f"{db_path}.backup"
    shutil.copy2(db_path, backup_path)
    logger.info(f"Created database backup at {backup_path}")
    return backup_path

def get_table_info(cursor, table_name):
    """Get information about table columns"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    return cursor.fetchall()

def get_indices(cursor, table_name):
    """Get all indices for a table"""
    cursor.execute(f"PRAGMA index_list({table_name})")
    return cursor.fetchall()

def get_index_info(cursor, index_name):
    """Get column information for an index"""
    cursor.execute(f"PRAGMA index_info({index_name})")
    return cursor.fetchall()

def migrate_database():
    """Perform the database migration"""
    if not os.path.exists(DB_PATH):
        logger.error(f"Database file not found at {DB_PATH}")
        return False
    
    # Create backup
    backup_path = backup_database(DB_PATH)
    
    # Connect to database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # Start transaction
        conn.execute("BEGIN TRANSACTION")
        
        # Get table info
        table_info = get_table_info(cursor, "transactions")
        current_columns = [col[1] for col in table_info]
        
        # Create a new table with the updated column names
        create_table_sql = "CREATE TABLE transactions_new ("
        column_defs = []
        
        for col in table_info:
            col_id, col_name, col_type, not_null, default_val, pk = col
            new_col_name = COLUMN_MAPPINGS.get(col_name, col_name)
            
            col_def = f"{new_col_name} {col_type}"
            if not_null:
                col_def += " NOT NULL"
            if default_val is not None:
                col_def += f" DEFAULT {default_val}"
            if pk:
                col_def += " PRIMARY KEY"
                
            column_defs.append(col_def)
        
        create_table_sql += ", ".join(column_defs) + ")"
        cursor.execute(create_table_sql)
        
        # Copy data from old table to new table
        old_cols = ", ".join(current_columns)
        new_cols = ", ".join([COLUMN_MAPPINGS.get(col, col) for col in current_columns])
        
        cursor.execute(f"INSERT INTO transactions_new SELECT {old_cols} FROM transactions")
        
        # Drop old table and rename new table
        cursor.execute("DROP TABLE transactions")
        cursor.execute("ALTER TABLE transactions_new RENAME TO transactions")
        
        # Recreate indices
        indices = get_indices(cursor, "transactions")
        for idx in indices:
            idx_name, unique, origin, partial = idx[1], idx[2], idx[3], idx[4]
            
            # Skip internal indices
            if origin == "pk":
                continue
                
            # Get columns in this index
            idx_columns = get_index_info(cursor, idx_name)
            idx_col_names = []
            
            for idx_col in idx_columns:
                col_pos, col_rank, col_name = idx_col
                # Map to new column name if needed
                new_col_name = COLUMN_MAPPINGS.get(col_name, col_name)
                idx_col_names.append(new_col_name)
            
            # Recreate the index
            unique_str = "UNIQUE " if unique else ""
            col_list = ", ".join(idx_col_names)
            
            # Drop old index if it exists
            cursor.execute(f"DROP INDEX IF EXISTS {idx_name}")
            
            # Create new index
            cursor.execute(f"CREATE {unique_str}INDEX {idx_name} ON transactions ({col_list})")
            
        # Commit transaction
        conn.commit()
        logger.info("Database migration completed successfully")
        return True
        
    except Exception as e:
        # Rollback on error
        conn.rollback()
        logger.error(f"Migration failed: {str(e)}")
        logger.info(f"The database has been restored from backup at {backup_path}")
        return False
        
    finally:
        conn.close()

def add_hash_column_and_index(db_path):
    """Adds the 'hash' column and an index on it to the transactions table."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        # Check if hash column exists
        table_info = get_table_info(cursor, "transactions")
        column_names = [col[1] for col in table_info]
        if 'hash' not in column_names:
            logger.info("Adding 'hash' column to transactions table.")
            cursor.execute("ALTER TABLE transactions ADD COLUMN hash TEXT")
            logger.info("Column 'hash' added successfully.")
        else:
            logger.info("'hash' column already exists.")

        # Check if index on hash column exists
        indices = get_indices(cursor, "transactions")
        index_names = [idx[1] for idx in indices]
        if 'idx_transactions_hash' not in index_names:
            logger.info("Creating index on 'hash' column.")
            cursor.execute("CREATE INDEX idx_transactions_hash ON transactions(hash)")
            logger.info("Index 'idx_transactions_hash' created successfully.")
        else:
            logger.info("Index 'idx_transactions_hash' already exists.")
        
        conn.commit()
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"Failed to add hash column and index: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    logger.info("Starting database schema update...")
    
    # First, run the column name consistency migration
    logger.info("Step 1: Running column name consistency migration...")
    consistency_success = migrate_database()
    if consistency_success:
        logger.info("Column name consistency check completed successfully.")
    else:
        logger.error("Column name consistency migration failed. Aborting further updates.")

    # Second, add the hash column and index if the first step was successful
    if consistency_success:
        logger.info("Step 2: Adding 'hash' column and index...")
        hash_success = add_hash_column_and_index(DB_PATH)
        if hash_success:
            logger.info("Successfully added 'hash' column and index.")
        else:
            logger.error("Failed to add 'hash' column and index.")

    logger.info("Database schema update process finished.")
