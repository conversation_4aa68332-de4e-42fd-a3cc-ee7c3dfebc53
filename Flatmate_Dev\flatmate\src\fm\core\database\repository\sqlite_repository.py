"""
SQLite implementation of the transaction repository.
"""

import os
import sqlite3
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd

from fm.core.config.config import config
from fm.core.services.logger import log
from fm.core.data_services.standards.columns import Columns
from fm.database_service.repository.transaction_repository import (
    ImportResult,
    Transaction,
    TransactionRepository,
)

class SQLiteTransactionRepository(TransactionRepository):
    """SQLite implementation of transaction repository."""

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the SQLite repository.

        Args:
            db_path: Optional custom path to the SQLite database file.
                     If not provided, uses the path from the application config.
        """
        if db_path is None:
            # Get the base data directory from config
            data_dir = config.get_path('data')
            # Create the database file path
            self.db_path = data_dir / 'transactions.db'
        else:
            self.db_path = Path(os.path.expanduser(db_path))
        
        # Ensure the parent directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        log.info(f"Using database at: {self.db_path}")
        
        self._ensure_db_exists()

    def _ensure_db_exists(self):
        """Ensure the database file and tables exist using Columns as the source of truth."""
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        with self._get_connection() as conn:
            cursor = conn.cursor()

            # Check if table exists
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'"
            )
            if cursor.fetchone() is None:
                # Define required columns and their SQL types based on StandardColumns
                columns = []

                # Map StandardColumns fields to SQL types using db_name for database column names
                for field in StandardColumns:
                    field_name = field.db_name  # Use db_name for database column names
                    # Determine appropriate SQL type
                    if field in [StandardColumns.DATE, StandardColumns.DETAILS, StandardColumns.UNIQUE_ID, 
                               StandardColumns.SOURCE_FILENAME, StandardColumns.ACCOUNT]:
                        columns.append(f'"{field_name}" TEXT')
                    elif field in [StandardColumns.AMOUNT, StandardColumns.BALANCE,
                                 StandardColumns.CREDIT_AMOUNT, StandardColumns.DEBIT_AMOUNT]:
                        columns.append(f'"{field_name}" REAL')
                    else:
                        columns.append(f'"{field_name}" TEXT')

                # Add system columns
                system_columns = [
                    "id INTEGER PRIMARY KEY",
                    "import_date TEXT",
                    "modified_date TEXT",
                    "is_deleted INTEGER DEFAULT 0",
                ]

                # Create table with all columns
                all_columns = columns + system_columns
                cursor.execute(
                    f"""
                CREATE TABLE transactions (
                    {', '.join(all_columns)}
                )
                """
                )

                # Create indexes for faster querying and duplicate detection
                # Use db_name for all column references
                date_col = StandardColumns.DATE.db_name
                desc_col = StandardColumns.DETAILS.db_name
                amount_col = StandardColumns.AMOUNT.db_name
                account_col = StandardColumns.ACCOUNT.db_name
                
                # Log the column names being used for debugging
                log.info(f"Creating indexes with columns: date={date_col}, desc={desc_col}, amount={amount_col}, account={account_col}")

                cursor.execute(
                    f'CREATE INDEX idx_transactions_date ON transactions("{date_col}")'
                )
                cursor.execute(
                    f'CREATE INDEX idx_transactions_details ON transactions("{desc_col}")'
                )
                cursor.execute(
                    f'CREATE INDEX idx_transactions_amount ON transactions("{amount_col}")'
                )
                if account_col:
                    cursor.execute(
                        f'CREATE INDEX idx_transactions_account ON transactions("{account_col}")'
                    )
                cursor.execute(
                    "CREATE INDEX idx_transactions_deleted ON transactions(is_deleted)"
                )

    def add_transactions_from_df(
        self, df: pd.DataFrame, source_file: Optional[str] = None
    ) -> ImportResult:
        """Add transactions from a DataFrame, handling duplicates efficiently."""
        result = ImportResult()

        if df.empty:
            log.warning("Empty DataFrame provided to add_transactions_from_df")
            return result

        # Make a copy to avoid modifying the original
        import_df = df.copy()

        # Log initial state
        log.info(
            f"Adding {len(import_df)} transactions to database from DataFrame"
        )
        log.info(f"DataFrame columns: {import_df.columns.tolist()}")

        # Check for required columns
        date_col = StandardColumns.DATE.value
        desc_col = StandardColumns.DETAILS.value
        amount_col = StandardColumns.AMOUNT.value
        account_col = StandardColumns.ACCOUNT.value

        required_cols = [date_col, desc_col, amount_col, account_col]
        missing_cols = [col for col in required_cols if col not in import_df.columns]

        if missing_cols:
            error_msg = f"Missing required columns: {missing_cols}"
            log.error(error_msg)
            result.error_count = len(import_df)
            result.errors.append(error_msg)
            return result

        # Add system fields
        now = datetime.now().isoformat()
        import_df["import_date"] = now
        import_df["modified_date"] = now
        import_df["is_deleted"] = 0

        if (
            source_file
            and StandardColumns.SOURCE_FILENAME.value not in import_df.columns
        ):
            import_df[StandardColumns.SOURCE_FILENAME.value] = source_file

        # Ensure date column is properly formatted
        if date_col in import_df.columns:
            # Check date format
            sample_dates = import_df[date_col].head(3).tolist()
            log.info(f"Sample dates before processing: {sample_dates}")

            # Ensure dates are strings in ISO format
            if pd.api.types.is_datetime64_any_dtype(import_df[date_col]):
                import_df[date_col] = import_df[date_col].dt.strftime("%Y-%m-%d")
                log.info("Converted datetime objects to ISO format strings")

        # Create a mapping from StandardColumns to database column names
        db_column_mapping = {
            # Map all StandardColumns to their database names
            **{col.value: col.db_name for col in StandardColumns},
            # Add system columns
            "import_date": "import_date",
            "modified_date": "modified_date",
            "is_deleted": "is_deleted"
        }

        # Rename DataFrame columns to match database columns
        renamed_df = import_df.copy()
        for std_col, db_col in db_column_mapping.items():
            if std_col in renamed_df.columns:
                renamed_df.rename(columns={std_col: db_col}, inplace=True)
                log.info(f"Mapped column '{std_col}' to database column '{db_col}'")

        # Create temporary table for import
        with self._get_connection() as conn:
            try:
                # Import to temporary table
                log.info("Creating temporary table for import")
                # Handle potential NaN values by converting them to None (NULL in SQLite)
                for col in renamed_df.columns:
                    if renamed_df[col].dtype == "float64":
                        renamed_df[col] = renamed_df[col].apply(
                            lambda x: None if pd.isna(x) else x
                        )
                renamed_df.to_sql("temp_import", conn, if_exists="replace", index=False)

                # Insert non-duplicate records
                cursor = conn.cursor()

                # Get all columns from temp table
                cursor.execute("PRAGMA table_info(temp_import)")
                temp_columns = [row[1] for row in cursor.fetchall()]
                log.info(f"Temp table columns: {temp_columns}")

                # Verify required columns exist in the import data
                date_col = db_column_mapping.get(StandardColumns.DATE.value, 'date')
                if date_col not in temp_columns:
                    error_msg = f"Required column '{date_col}' (Date) is missing from import data"
                    log(error_msg, level="error")
                    raise ValueError(error_msg)

                # Define required columns with their display names
                required_columns = {
                    StandardColumns.DATE.value: "Date",
                    StandardColumns.DETAILS.value: "Details",
                    StandardColumns.AMOUNT.value: "Amount",
                    StandardColumns.ACCOUNT.value: "Account"
                }
                
                # Verify required columns exist in import data
                missing_columns = [
                    display_name 
                    for col, display_name in required_columns.items()
                    if db_column_mapping.get(col, col.lower().replace(' ', '_')) not in temp_columns
                ]
                
                if missing_columns:
                    error_msg = f"Required columns missing from import data: {', '.join(missing_columns)}"
                    log(error_msg, level="error")
                    raise ValueError(error_msg)
                    
                # Check for NULL account numbers in the import data
                cursor.execute(f'SELECT COUNT(*) FROM temp_import WHERE "{db_column_mapping[StandardColumns.ACCOUNT.value]}" IS NULL')
                null_accounts = cursor.fetchone()[0]
                if null_accounts > 0:
                    error_msg = f"Found {null_accounts} records with NULL account numbers. Account number is required for all transactions."
                    log.error(error_msg)
                    raise ValueError(error_msg)
                
                # Use all columns from temp table for import
                # The database schema will handle any missing columns as NULL or defaults
                import_columns = temp_columns
                log.info(f"Importing columns: {import_columns}")

                columns_str = ", ".join([f'"{col}"' for col in import_columns])

                log.info(f"Using columns for import: {import_columns}")

                # Check if we have sample data in the temp table
                cursor.execute("SELECT * FROM temp_import LIMIT 3")
                sample_rows = cursor.fetchall()
                if sample_rows:
                    log.info(f"Sample data from temp table: {sample_rows}")

                # Insert non-duplicates
                log.info("Inserting non-duplicate records")

                # Map the StandardColumns column names to database column names for the WHERE clause
                db_date_col = db_column_mapping.get(date_col, "date")
                db_desc_col = db_column_mapping.get(desc_col, "description")
                db_amount_col = db_column_mapping.get(amount_col, "amount")

                # Get additional columns for the duplicate check
                db_account_col = db_column_mapping.get(
                    StandardColumns.ACCOUNT.value, "account_number"
                )
                db_balance_col = db_column_mapping.get(
                    StandardColumns.BALANCE.value, "balance"
                )
                db_unique_id_col = db_column_mapping.get(
                    StandardColumns.UNIQUE_ID.value, "unique_id"
                )

                # Check if balance or unique_id columns exist in the temp table
                has_balance = db_balance_col in import_columns
                has_unique_id = db_unique_id_col in import_columns

                # Build the uniqueness check based on available columns
                uniqueness_check = f"""
                    "{db_date_col}" = t."{db_date_col}" 
                    AND "{db_desc_col}" = t."{db_desc_col}" 
                    AND "{db_amount_col}" = t."{db_amount_col}"
                    AND "{db_account_col}" = t."{db_account_col}"
                """

                # Add balance check if available
                if has_balance:
                    uniqueness_check += f""" AND (("{db_balance_col}" IS NULL AND t."{db_balance_col}" IS NULL) OR "{db_balance_col}" = t."{db_balance_col}")"""
                # Add unique_id check if available
                elif has_unique_id:
                    uniqueness_check += f""" AND (("{db_unique_id_col}" IS NULL AND t."{db_unique_id_col}" IS NULL) OR "{db_unique_id_col}" = t."{db_unique_id_col}")"""

                # Use the uniqueness check in the query
                insert_query = f"""
                INSERT INTO transactions ({columns_str})
                SELECT {columns_str} FROM temp_import t
                WHERE NOT EXISTS (
                    SELECT 1 FROM transactions 
                    WHERE {uniqueness_check}
                    AND is_deleted = 0
                )
                """

                log.debug(f"Executing insert query: {insert_query}")
                cursor.execute(insert_query)
                result.added_count = cursor.rowcount
                log.info(f"Added {result.added_count} new records")

                # Count duplicates
                # Use the same uniqueness check for counting duplicates
                duplicate_query = f"""
                SELECT COUNT(*) FROM temp_import t
                WHERE EXISTS (
                    SELECT 1 FROM transactions 
                    WHERE {uniqueness_check}
                    AND is_deleted = 0
                )
                """

                cursor.execute(duplicate_query)
                result.duplicate_count = cursor.fetchone()[0]
                log.info(f"Found {result.duplicate_count} duplicate records")

                # If no records were added or found as duplicates, check for data issues
                if result.added_count == 0 and result.duplicate_count == 0:
                    log.warning("No records added or found as duplicates. Checking for data issues.")

                    # Check a sample transaction from the database
                    cursor.execute(
                        f'SELECT "{date_col}", "{desc_col}", "{amount_col}" FROM transactions LIMIT 3'
                    )
                    db_samples = cursor.fetchall()
                    if db_samples:
                        log.info(f"Sample transactions in database: {db_samples}")
                    else:
                        log.info("No existing transactions in database")

            except Exception as e:
                error_msg = f"Error adding transactions to database: {str(e)}"
                log.error(error_msg)
                import traceback

                log.error(f"Traceback: {traceback.format_exc()}")
                result.error_count = len(import_df)
                result.errors.append(error_msg)

            finally:
                # Clean up
                try:
                    cursor.execute("DROP TABLE IF EXISTS temp_import")
                    log.debug("Temporary table dropped")
                except Exception as e:
                    log.error(f"Error dropping temporary table: {str(e)}")

            return result

    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """Add a list of Transaction objects to the database by converting to DataFrame first."""
        if not transactions:
            return ImportResult()

        # Convert Transaction objects to DataFrame
        data = []
        for transaction in transactions:
            # Create a dict representation of the transaction
            trans_dict = {}

            # Map Transaction attributes to StandardColumns fields using db_name
            trans_dict[StandardColumns.DATE.db_name] = (
                transaction.date.isoformat() if transaction.date else None
            )
            trans_dict[StandardColumns.DETAILS.db_name] = transaction.description
            trans_dict[StandardColumns.AMOUNT.db_name] = transaction.amount

            # Map other fields if they exist in StandardColumns
            if transaction.account_number:
                trans_dict[StandardColumns.ACCOUNT.db_name] = transaction.account_number

            if transaction.transaction_type:
                trans_dict[StandardColumns.PAYMENT_TYPE.db_name] = transaction.transaction_type

            if transaction.category:
                trans_dict["category"] = transaction.category

            if transaction.notes:
                trans_dict["notes"] = transaction.notes

            if transaction.tags:
                trans_dict["tags"] = transaction.tags

            if transaction.source_bank:
                trans_dict["source_bank"] = transaction.source_bank

            if transaction.source_file:
                trans_dict[StandardColumns.SOURCE_FILENAME.db_name] = transaction.source_file

            data.append(trans_dict)

        # Create DataFrame and add transactions
        df = pd.DataFrame(data)
        return self.add_transactions_from_df(df)

    def get_transactions_as_df(self, filters: Optional[Dict] = None) -> pd.DataFrame:
        """Get transactions as a pandas DataFrame with optional filtering."""
        query = "SELECT * FROM transactions WHERE is_deleted = 0"
        params = []

        if filters:
            # Use db_name for database column names
            date_col = StandardColumns.DATE.db_name
            desc_col = StandardColumns.DETAILS.db_name
            amount_col = StandardColumns.AMOUNT.db_name
            account_col = StandardColumns.ACCOUNT.db_name

            # Add filter conditions
            if "start_date" in filters:
                query += f' AND "{date_col}" >= ?'
                params.append(filters["start_date"])
            if "end_date" in filters:
                query += f' AND "{date_col}" <= ?'
                params.append(filters["end_date"])
            if "account_number" in filters and account_col:
                query += f' AND "{account_col}" = ?'
                params.append(filters["account_number"])
            if "transaction_type" in filters:
                query += f' AND "{StandardColumns.PAYMENT_TYPE.db_name}" = ?'
                params.append(filters["transaction_type"])
            if "category" in filters:
                query += f" AND category = ?"
                params.append(filters["category"])
            if "min_amount" in filters:
                query += f' AND "{amount_col}" >= ?'
                params.append(filters["min_amount"])
            if "max_amount" in filters:
                query += f' AND "{amount_col}" <= ?'
                params.append(filters["max_amount"])
            if "search" in filters:
                search_term = f"%{filters['search']}%"
                query += f' AND ("{desc_col}" LIKE ? OR notes LIKE ?)'
                params.extend([search_term, search_term])
            # Add tags filter
            if "tags" in filters:
                tags = filters["tags"]
                if isinstance(tags, list):
                    # If multiple tags, search for any of them
                    placeholders = ", ".join(["?"] * len(tags))
                    query += f" AND tags IN ({placeholders})"
                    params.extend(tags)
                else:
                    # Single tag
                    query += " AND tags = ?"
                    params.append(tags)

        with self._get_connection() as conn:
            return pd.read_sql(query, conn, params=params)

    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """Get transactions based on optional filters."""
        # Get transactions as DataFrame
        df = self.get_transactions_as_df(filters)

        # Convert DataFrame rows to Transaction objects
        return [self._row_to_transaction(row) for _, row in df.iterrows()]

    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """Get a transaction by its ID."""
        with self._get_connection() as conn:
            df = pd.read_sql(
                "SELECT * FROM transactions WHERE id = ? AND is_deleted = 0",
                conn,
                params=[transaction_id],
            )

            if not df.empty:
                return self._row_to_transaction(df.iloc[0])
            return None

    def update_transaction(
        self, transaction_id: Optional[int], data: Dict[str, Any]
    ) -> bool:
        """Update a transaction in the database.

        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update

        Returns:
            True if update was successful
        """
        if not transaction_id:
            return False

        # Create a dict of fields to update
        update_data = {}

        # Set modified date
        modified_date = datetime.now()
        update_data["modified_date"] = modified_date.isoformat()

        # Map fields from data to column names using db_name
        # Get the list of columns that are safe for users to edit.
        user_editable_columns = {col.db_name for col in Columns.get('user_editable')}

        # Filter the incoming data to only include user-editable fields.
        for key, value in data.items():
            # The key from the data dict should correspond to a db_name
            if key in user_editable_columns:
                update_data[key] = value
            else:
                log.warning(f"Attempted to update non-editable field '{key}'. Ignoring.")

        # If no fields to update, return early
        if not update_data:
            return False

        # Build update query
        set_clause = ", ".join([f'"{k}" = ?' for k in update_data.keys()])
        params = list(update_data.values()) + [transaction_id]

        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                f"""
            UPDATE transactions 
            SET {set_clause}
            WHERE id = ? AND is_deleted = 0
            """,
                params,
            )

            return cursor.rowcount > 0

    def delete_transaction(self, transaction_id: int) -> bool:
        """Soft delete a transaction by marking it as deleted."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
            UPDATE transactions 
            SET is_deleted = 1, modified_date = ?
            WHERE id = ? AND is_deleted = 0
            """,
                (datetime.now().isoformat(), transaction_id),
            )

            return cursor.rowcount > 0

    def delete_all_transactions(self) -> int:
        """Soft delete all transactions by marking them as deleted."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
            UPDATE transactions 
            SET is_deleted = 1, modified_date = ?
            WHERE is_deleted = 0
            """,
                (datetime.now().isoformat(),),
            )

            return cursor.rowcount
        ''' # TODO review this code - recently added by Augment Code'''
    def get_available_columns(self, include_system_columns: bool = True) -> List[str]:
        """Get all available columns from the transactions table schema.

        Args:
            include_system_columns: If True (default), includes id, import_date, modified_date.
                                  Always excludes is_deleted as it's not useful for UI.

        Returns:
            List of column names available for display/filtering
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(transactions)")
            columns_info = cursor.fetchall()

            if include_system_columns:
                # Only exclude is_deleted (not useful for UI)
                excluded_columns = {'is_deleted'}
            else:
                # Exclude all system columns (legacy behavior)
                excluded_columns = {'id', 'import_date', 'modified_date', 'is_deleted'}

            available_columns = [
                col_info[1] for col_info in columns_info
                if col_info[1] not in excluded_columns
            ]

            log.info(f"Available database columns (include_system={include_system_columns}): {available_columns}", level="info")
            return available_columns

    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the stored transactions."""
        # Use db_name for database column names
        date_col = StandardColumns.DATE.db_name
        amount_col = StandardColumns.AMOUNT.db_name
        account_col = StandardColumns.ACCOUNT.db_name

        with self._get_connection() as conn:
            cursor = conn.cursor()

            try:
                # Get total count of active transactions
                cursor.execute(
                    f"""
                SELECT COUNT(*) FROM transactions WHERE is_deleted = 0
                """
                )
                total_count = cursor.fetchone()[0]

                # Get earliest and latest transaction dates
                cursor.execute(
                    f"""
                SELECT MIN("{date_col}"), MAX("{date_col}") FROM transactions WHERE is_deleted = 0
                """
                )
                date_range = cursor.fetchone()
                earliest_date = date_range[0] if date_range and date_range[0] else None
                latest_date = date_range[1] if date_range and date_range[1] else None

                # Get count by category
                cursor.execute(
                    f"""
                SELECT category, COUNT(*) 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY category
                """
                )
                categories = {
                    row[0] or "Uncategorized": row[1] for row in cursor.fetchall()
                }

                # Get sum by category
                cursor.execute(
                    f"""
                SELECT category, SUM("{amount_col}") 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY category
                """
                )
                category_sums = {
                    row[0] or "Uncategorized": row[1] for row in cursor.fetchall()
                }

                # Get count by account number
                cursor.execute(
                    f"""
                SELECT "{account_col}", COUNT(*) 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY "{account_col}"
                """
                )
                accounts = {row[0] or "Unknown": row[1] for row in cursor.fetchall()}

                # Get sum by account number
                cursor.execute(
                    f"""
                SELECT "{account_col}", SUM("{amount_col}") 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY "{account_col}"
                """
                )
                account_sums = {
                    row[0] or "Unknown": row[1] for row in cursor.fetchall()
                }

                return {
                    "total_count": total_count,
                    "earliest_date": earliest_date,
                    "latest_date": latest_date,
                    "categories": categories,
                    "category_sums": category_sums,
                    "accounts": accounts,
                    "account_sums": account_sums,
                }

            except Exception as e:
                log.error(f"Error getting transaction statistics: {e}")
                return {
                    "total_count": 0,
                    "earliest_date": None,
                    "latest_date": None,
                    "categories": {},
                    "category_sums": {},
                    "accounts": {},
                    "account_sums": {},
                }

    def _row_to_transaction(self, row) -> Transaction:
        """Convert a DataFrame row or SQLite row to a Transaction object."""
        # Get the row as a dictionary
        if isinstance(row, pd.Series):
            row_dict = row.to_dict()
        else:
            row_dict = dict(row)

        # Extract values using db_name for consistent column access
        date_col = StandardColumns.DATE.db_name
        desc_col = StandardColumns.DETAILS.db_name
        amount_col = StandardColumns.AMOUNT.db_name
        account_col = StandardColumns.ACCOUNT.db_name
        
        # Parse date
        date_str = row_dict.get(date_col)
        if date_str:
            try:
                date = datetime.fromisoformat(date_str.split('T')[0])
            except (ValueError, AttributeError):
                date = None
        else:
            date = None
        
        # Get other fields
        description = row_dict.get(desc_col, "")
        amount = row_dict.get(amount_col, 0.0)
        account_number = row_dict.get(account_col)
        transaction_type = row_dict.get(StandardColumns.PAYMENT_TYPE.db_name)
        category = row_dict.get("category")
        notes = row_dict.get("notes")
        tags = row_dict.get("tags", "")  # Get tags with empty string as default
        source_bank = row_dict.get("source_bank")
        source_file = row_dict.get(StandardColumns.SOURCE_FILENAME.db_name)
        import_date = row_dict.get("import_date")
        modified_date = row_dict.get("modified_date")

        return Transaction(
            transaction_id=row_dict.get("id"),
            date=date,
            description=description,
            amount=amount,
            account_number=account_number or "",
            transaction_type=transaction_type or "",
            category=category or "",
            notes=notes or "",
            tags=tags or "",  # Include tags in the Transaction object
            source_bank=source_bank or "",
            source_file=source_file or "",
            import_date=import_date,
            modified_date=modified_date,
        )

    def _get_connection(self):
        """Get a connection to the SQLite database."""
        return sqlite3.connect(self.db_path)


