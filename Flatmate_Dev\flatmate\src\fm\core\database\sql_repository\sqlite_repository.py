"""
SQLite implementation of the transaction repository.
"""
from __future__ import annotations

import hashlib
import os
import sqlite3
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Union

import pandas as pd

from fm.core.config.config import config
from fm.core.services.logger import log
from fm.core.data_services.standards.columns import Columns, Column

if TYPE_CHECKING:
    from .transaction_repository import ITransactionRepository, ImportResult, Transaction


class SQLiteTransactionRepository(ITransactionRepository):
    """SQLite implementation of transaction repository."""

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the SQLite repository.

        Args:
            db_path: Optional custom path to the SQLite database file.
                     If not provided, uses the path from the application config.
        """
        if db_path is None:
            data_dir = config.get_path('data')
            self.db_path = data_dir / 'transactions.db'
        else:
            self.db_path = Path(os.path.expanduser(db_path))
        
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        log.info(f"Using database at: {self.db_path}")
        
        self._ensure_db_exists()

    def _ensure_db_exists(self):
        """Ensure the database file and tables exist using the Columns registry as the source of truth."""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        with self._get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'"
            )
            if cursor.fetchone() is None:
                columns_to_create = []
                for col in Columns.get_transaction_columns():
                    if col.dtype in [datetime, pd.Timestamp, 'datetime64[ns]']:
                        sql_type = 'TEXT'
                    elif col.dtype in [float, int, 'float64', 'int64']:
                        sql_type = 'REAL'
                    else:
                        sql_type = 'TEXT'
                    columns_to_create.append(f'"{col.db_name}" {sql_type}')

                system_columns = [
                    "id INTEGER PRIMARY KEY",
                    "import_date TEXT",
                    "modified_date TEXT",
                    "is_deleted INTEGER DEFAULT 0",
                ]

                all_columns = columns_to_create + system_columns
                create_table_sql = f"CREATE TABLE transactions ({', '.join(all_columns)})"
                cursor.execute(create_table_sql)

                date_col = Columns.DATE.db_name
                desc_col = Columns.DETAILS.db_name
                amount_col = Columns.AMOUNT.db_name
                account_col = Columns.ACCOUNT.db_name
                
                log.debug(f"Creating index on columns: {date_col}, {desc_col}, {amount_col}, {account_col}")

                cursor.execute(
                    f'CREATE INDEX idx_transactions_unique ON transactions ("{date_col}", "{desc_col}", "{amount_col}", "{account_col}")'
                )
                conn.commit()

    def add_transactions_from_df(
        self, df: pd.DataFrame, source_file: Optional[str] = None
    ) -> ImportResult:
        """Add transactions from a DataFrame that has been prepared by DBIOService."""
        if df.empty:
            return ImportResult(0, 0, 0)

        all_db_cols = [c.db_name for c in Columns.get_all_columns()]
        required_cols = [c for c in all_db_cols if c in df.columns]
        df = df[required_cols].copy()

        df["import_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df["modified_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df["is_deleted"] = 0

        def generate_unique_id(row):
            key_fields = [
                str(row.get(Columns.DATE.db_name, "")),
                str(row.get(Columns.DETAILS.db_name, "")),
                str(row.get(Columns.AMOUNT.db_name, "")),
                str(row.get(Columns.ACCOUNT.db_name, "")),
            ]
            return hashlib.sha1("".join(key_fields).encode()).hexdigest()

        df[Columns.UNIQUE_ID.db_name] = df.apply(generate_unique_id, axis=1)

        with self._get_connection() as conn:
            try:
                df.to_sql("temp_transactions", conn, if_exists="replace", index=False)

                cursor = conn.cursor()

                cursor.execute("SELECT COUNT(*) FROM temp_transactions")
                total_to_import = cursor.fetchone()[0]

                db_cols = [f'"{c}"' for c in df.columns]
                
                insert_sql = f"""
                INSERT INTO transactions ({', '.join(db_cols)})
                SELECT {', '.join(db_cols)} FROM temp_transactions
                WHERE NOT EXISTS (
                    SELECT 1 FROM transactions t2
                    WHERE t2."{Columns.UNIQUE_ID.db_name}" = temp_transactions."{Columns.UNIQUE_ID.db_name}"
                )
                """
                cursor.execute(insert_sql)
                
                newly_inserted = cursor.rowcount
                conn.commit()

                cursor.execute("DROP TABLE temp_transactions")

                duplicates = total_to_import - newly_inserted

                return ImportResult(
                    total_to_import=total_to_import,
                    added_count=newly_inserted,
                    duplicate_count=duplicates,
                )

            except Exception as e:
                log.error(f"Error adding transactions from DataFrame: {e}", exc_info=True)
                return ImportResult(0, 0, 0)

    def add_transactions(self, transactions: List[Transaction]):
        """Add a list of Transaction objects to the database by converting to DataFrame first."""
        if not transactions:
            return

        transaction_dicts = [t.to_dict() for t in transactions]
        df = pd.DataFrame(transaction_dicts)

        if Columns.DATE.db_name in df.columns:
            df[Columns.DATE.db_name] = pd.to_datetime(df[Columns.DATE.db_name]).dt.strftime(
                "%Y-%m-%d"
            )

        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df["import_date"] = now
        df["modified_date"] = now
        df["is_deleted"] = 0

        self.add_transactions_from_df(df)

    def get_transactions_as_df(self, filters: Optional[Dict] = None) -> pd.DataFrame:
        """Get transactions as a pandas DataFrame with optional filtering."""
        with self._get_connection() as conn:
            query = "SELECT * FROM transactions WHERE is_deleted = 0"
            params = []

            if filters:
                conditions = []
                for key, value in filters.items():
                    if key == "start_date":
                        conditions.append(f'"{Columns.DATE.db_name}" >= ?')
                        params.append(value)
                    elif key == "end_date":
                        conditions.append(f'"{Columns.DATE.db_name}" <= ?')
                        params.append(value)
                    elif key == "account_number":
                        conditions.append(f'"{Columns.ACCOUNT.db_name}" = ?')
                        params.append(value)
                    elif key == "description":
                        conditions.append(f'"{Columns.DETAILS.db_name}" LIKE ?')
                        params.append(f"%{value}%")
                    else:
                        conditions.append(f'"{key}" = ?')
                        params.append(value)

                if conditions:
                    query += " AND " + " AND ".join(conditions)

            query += f' ORDER BY "{Columns.DATE.db_name}" DESC'

            try:
                df = pd.read_sql_query(query, conn, params=params)
                return df
            except Exception as e:
                log.error(f"Error getting transactions as DataFrame: {e}")
                return pd.DataFrame()

    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """Get transactions based on optional filters."""
        df = self.get_transactions_as_df(filters)
        if df.empty:
            return []
        # Convert each row (which is a pandas Series) to a dictionary and then to a Transaction object
        return [Transaction.from_dict(row.to_dict()) for _, row in df.iterrows()]

    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """Get a transaction by its ID."""
        df = self.get_transactions_as_df(filters={'id': transaction_id})
        if not df.empty:
            # Convert the first row to a dictionary and then to a Transaction object
            return Transaction.from_dict(df.iloc[0].to_dict())
        return None

    def update_transaction(
        self, transaction_id: Optional[int], data: Dict[str, Any]
    ) -> bool:
        """
        Update a transaction in the database.

        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update

        Returns:
            True if update was successful
        """
        if not data:
            return False

        with self._get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("PRAGMA table_info(transactions)")
            valid_columns = {row[1] for row in cursor.fetchall()}
            
            system_cols = {'id', 'import_date', 'modified_date', 'is_deleted', Columns.UNIQUE_ID.db_name}
            
            update_data = {k: v for k, v in data.items() if k in valid_columns and k not in system_cols}

            if not update_data:
                log.warning("No valid fields to update.")
                return False

            update_data["modified_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            set_clause = ", ".join([f'"{key}" = ?' for key in update_data.keys()])
            params = list(update_data.values())

            if transaction_id:
                where_clause = "id = ?"
                params.append(transaction_id)
            else:
                date_val = data.get(Columns.DATE.db_name)
                desc_val = data.get(Columns.DETAILS.db_name)
                amount_val = data.get(Columns.AMOUNT.db_name)

                if not all([date_val, desc_val, amount_val]):
                    log.error("Cannot update transaction without ID or composite key.")
                    return False
                
                where_clause = f'"{Columns.DATE.db_name}" = ? AND "{Columns.DETAILS.db_name}" = ? AND "{Columns.AMOUNT.db_name}" = ?'
                params.extend([date_val, desc_val, amount_val])

            try:
                cursor.execute(
                    f"UPDATE transactions SET {set_clause} WHERE {where_clause}", params
                )
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                log.error(f"Error updating transaction: {e}")
                return False

    def delete_transaction(self, transaction_id: int) -> bool:
        """Soft delete a transaction by marking it as deleted."""
        return self.update_transaction(
            transaction_id, {"is_deleted": 1}
        )

    def delete_all_transactions(self) -> bool:
        """Soft delete all transactions by marking them as deleted."""
        with self._get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE transactions SET is_deleted = 1, modified_date = ?",
                    (datetime.now().strftime("%Y-%m-%d %H:%M:%S"),),
                )
                conn.commit()
                return True
            except Exception as e:
                log.error(f"Error deleting all transactions: {e}")
                return False

    def get_available_columns(self, include_system_columns: bool = True) -> List[str]:
        """
        Get all available columns from the transactions table schema.

        Args:
            include_system_columns: If True (default), includes id, import_date, modified_date.
                                  Always excludes is_deleted as it's not useful for UI.

        Returns:
            List of column names available for display/filtering
        """
        with self._get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(transactions)")
                all_columns = [row[1] for row in cursor.fetchall()]

                excluded_cols = {'is_deleted'}
                if not include_system_columns:
                    excluded_cols.update({'id', 'import_date', 'modified_date'})

                return [col for col in all_columns if col not in excluded_cols]

            except Exception as e:
                log.error(f"Error getting available columns: {e}")
                return []

    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the stored transactions."""
        with self._get_connection() as conn:
            try:
                cursor = conn.cursor()

                date_col = Columns.DATE.db_name
                amount_col = Columns.AMOUNT.db_name
                account_col = Columns.ACCOUNT.db_name

                cursor.execute("SELECT COUNT(*) FROM transactions WHERE is_deleted = 0")
                total_count = cursor.fetchone()[0]

                cursor.execute(
                    f'SELECT MIN("{date_col}"), MAX("{date_col}") FROM transactions WHERE is_deleted = 0'
                )
                date_range = cursor.fetchone()
                earliest_date = date_range[0] if date_range and date_range[0] else None
                latest_date = date_range[1] if date_range and date_range[1] else None

                cursor.execute(
                    f"""
                SELECT category, COUNT(*) 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY category
                """
                )
                categories = {
                    row[0] or "Uncategorized": row[1] for row in cursor.fetchall()
                }

                cursor.execute(
                    f"""
                SELECT category, SUM("{amount_col}") 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY category
                """
                )
                category_sums = {
                    row[0] or "Uncategorized": row[1] for row in cursor.fetchall()
                }

                cursor.execute(
                    f"""
                SELECT "{account_col}", COUNT(*) 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY "{account_col}"
                """
                )
                accounts = {row[0] or "Unknown": row[1] for row in cursor.fetchall()}

                cursor.execute(
                    f"""
                SELECT "{account_col}", SUM("{amount_col}") 
                FROM transactions 
                WHERE is_deleted = 0 
                GROUP BY "{account_col}"
                """
                )
                account_sums = {
                    row[0] or "Unknown": row[1] for row in cursor.fetchall()
                }

                return {
                    "total_count": total_count,
                    "earliest_date": earliest_date,
                    "latest_date": latest_date,
                    "categories": categories,
                    "category_sums": category_sums,
                    "accounts": accounts,
                    "account_sums": account_sums,
                }

            except Exception as e:
                log.error(f"Error getting transaction statistics: {e}")
                return {
                    "total_count": 0,
                    "earliest_date": None,
                    "latest_date": None,
                    "categories": {},
                    "category_sums": {},
                    "accounts": {},
                    "account_sums": {},
                }


    def _get_connection(self):
        """Get a connection to the SQLite database and register custom functions."""
        conn = sqlite3.connect(self.db_path)
        
        def sha1_hash(text):
            if text is None:
                return None
            return hashlib.sha1(str(text).encode('utf-8')).hexdigest()

        conn.create_function("sha1", 1, sha1_hash)
        
        return conn
