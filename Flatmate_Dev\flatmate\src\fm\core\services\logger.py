#!/usr/bin/env python3
"""
FlatMate Centralized Logger (Standard Logging Wrapper)
====================================================

This module provides a centralized logger for the FlatMate application.
It acts as a non-breaking wrapper around Python's standard `logging` module,
_preserving the original simple API while gaining performance, thread-safety,
and robustness._

Usage (No Changes):
    from fm.core.services.logger import log
    log.info('A message')
    log.error('Something went wrong')

Configuration:
    - Log level and directory are read from the global config manager.
    - Log rotation (keeping the 3 most recent session logs) is handled automatically.
"""

# ===========================
# Imports
# ===========================
import os
import sys
import logging
import inspect
from pathlib import Path
from datetime import datetime
from enum import Enum
from typing import Optional, Dict

import coloring
from fm.core.config import config
from fm.core.config.keys import ConfigKeys

# ===========================
# Public API
# ===========================
__all__ = [
    "log",
    "LogLevel"
]

# ===========================
# Constants & Configuration
# ===========================
class LogLevel(Enum):
    """Standardized log levels (mirrors original API)"""
    DEBUG = 'DEBUG'
    INFO = 'INFO'
    WARNING = 'WARNING'
    ERROR = 'ERROR'
    CRITICAL = 'CRITICAL'

# Map our LogLevel enum to the standard logging levels
_LEVEL_MAP: Dict[LogLevel, int] = {
    LogLevel.DEBUG: logging.DEBUG,
    LogLevel.INFO: logging.INFO,
    LogLevel.WARNING: logging.WARNING,
    LogLevel.ERROR: logging.ERROR,
    LogLevel.CRITICAL: logging.CRITICAL,
}

# ===========================
# Core Logging Class
# ===========================
class _Logger:
    """
    Internal logger class that wraps the standard `logging` module.
    This preserves the original `log.info()` style API.
    """
    def __init__(self):
        # Initialize the logger with a unique name to avoid conflicts
        self.logger = logging.getLogger('flatmate.core')
        
        # Remove any existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
            
        # Reset to basic config to clear any existing configurations
        logging.basicConfig(level=logging.NOTSET, handlers=[])
        
        # Set our logger to capture all levels
        self.logger.setLevel(logging.DEBUG)
        self.logger.propagate = False  # Prevent propagation to root logger
        
        # Create logs directory if it doesn't exist
        log_dir = Path.home() / '.flatmate' / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create a unique log file for this session
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_filepath = log_dir / f'flatmate-{timestamp}.log'
        
        # Keep only the 3 most recent log files
        self._rotate_logs(log_dir)
        
        # Create and configure handlers
        try:
            # 1. Get console log level from config, default to INFO
            console_level_str = config.get_value(ConfigKeys.Logging.CONSOLE_LOG_LEVEL, 'INFO').upper()
            console_level = getattr(logging, console_level_str, logging.INFO)

            # 2. Create a console handler with colors
            console_handler = logging.StreamHandler()
            
            # Define color codes
            COLOR_RESET = '\033[0m'
            COLOR_DEBUG = '\033[36m'    # Cyan
            COLOR_INFO = '\033[32m'     # Green
            COLOR_WARNING = '\033[33m'   # Yellow
            COLOR_ERROR = '\033[31m'     # Red
            COLOR_CRITICAL = '\033[1;31m' # Bright Red
            
            class ColoredFormatter(logging.Formatter):
                def format(self, record):
                    module_name = getattr(record, 'module_name', 'UNKNOWN')
                    if record.levelno >= logging.CRITICAL:
                        color = COLOR_CRITICAL
                    elif record.levelno >= logging.ERROR:
                        color = COLOR_ERROR
                    elif record.levelno >= logging.WARNING:
                        color = COLOR_WARNING
                    elif record.levelno >= logging.INFO:
                        color = COLOR_INFO
                    else:  # DEBUG
                        color = COLOR_DEBUG
                    levelname = f"{color}{record.levelname}{COLOR_RESET}"
                    return f"[{module_name}] [{levelname}] {record.msg}"
            
            console_handler.setFormatter(ColoredFormatter())
            console_handler.setLevel(console_level)

            # 3. Create a file handler
            file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - [%(module_name)s] [%(levelname)s] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            file_handler.setLevel(logging.DEBUG)  # Always log DEBUG to file

            # 4. Add handlers to the logger
            self.logger.addHandler(console_handler)
            self.logger.addHandler(file_handler)

        except Exception as e:
            sys.stderr.write(f"[CRITICAL] Logger setup failed during handler configuration: {e}\n")
            # Fallback to a basic config to ensure logs are not lost
            logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
            self.logger.critical(f"Logger setup failed: {e}. Using basic config.")

    def _rotate_logs(self, log_dir: Path):
        """Keeps the 3 most recent log files, deleting older ones."""
        try:
            # Sort files by modification time, newest first.
            log_files = sorted(log_dir.glob('flatmate-*.log'), key=os.path.getmtime, reverse=True)
            # Keep the 3 most recent logs and delete any others.
            for old_log in log_files[3:]:
                try:
                    old_log.unlink()
                except OSError as e:
                    sys.stderr.write(f"Error removing old log file {old_log}: {e}\n")
        except Exception as e:
            sys.stderr.write(f"Error during log rotation: {e}\n")

    def _log(self, level: LogLevel, message: str, module: Optional[str], exc_info: bool) -> None:
        """
        Core logging method that maps to the standard logger.

        Args:
            level: The logging level as a LogLevel enum member.
            message: The log message.
            module: Optional module name override.
            exc_info: If True, exception information is added.
        """
        # Determine the source module if not provided
        module_to_log = module if module else _determine_log_source()
        
        # Get the integer level for the standard logger
        numeric_level = _LEVEL_MAP.get(level, logging.INFO)

        # Use 'extra' to pass the module name to the formatter
        self.logger.log(numeric_level, message, exc_info=exc_info, extra={'module_name': module_to_log})

    def info(self, message: str, module: Optional[str] = None) -> None:
        self._log(level=LogLevel.INFO, message=message, module=module, exc_info=False)

    def error(self, message: str, module: Optional[str] = None, exc_info: bool = False) -> None:
        self._log(level=LogLevel.ERROR, message=message, module=module, exc_info=exc_info)

    def debug(self, message: str, module: Optional[str] = None) -> None:
        self._log(level=LogLevel.DEBUG, message=message, module=module, exc_info=False)

    def warning(self, message: str, module: Optional[str] = None) -> None:
        self._log(level=LogLevel.WARNING, message=message, module=module, exc_info=False)

    def critical(self, message: str, module: Optional[str] = None, exc_info: bool = True) -> None:
        self._log(level=LogLevel.CRITICAL, message=message, module=module, exc_info=exc_info)

    def set_level(self, level: str) -> None:
        """Temporarily overrides the console log level for the current session."""
        try:
            numeric_level = getattr(logging, level.upper(), None)
            if not isinstance(numeric_level, int):
                self.warning(f"Invalid log level '{level}' provided to set_level.")
                return

            # Find the console handler and update its level
            for handler in self.logger.handlers:
                if isinstance(handler, logging.StreamHandler):
                    handler.setLevel(numeric_level)
                    # Use a direct print to avoid being filtered by the new level
                    print(f"[LOGGER] Console log level set to {level.upper()}")
                    return
        except Exception as e:
            self.error(f"Failed to set log level to {level}: {e}", exc_info=True)

# ===========================
# Internal Helper Functions
# ===========================
def _determine_log_source() -> str:
    """
    Determines the module name of the code that called the logger.
    Skips all logger-related frames to find the actual caller.
    
    Returns:
        Module name or a fallback value.
    """
    try:
        frame = inspect.currentframe()
        if not frame:
            return 'UNKNOWN_MODULE'
            
        # Go back until we are out of the logger's own module
        current_frame = frame
        while current_frame:
            frame_module = current_frame.f_globals.get('__name__')
            if frame_module == __name__:
                current_frame = current_frame.f_back
                continue
            
            # We've found the first frame outside the logger module.
            if frame_module == '__main__':
                filepath = current_frame.f_globals.get('__file__')
                return os.path.splitext(os.path.basename(filepath))[0] if filepath else '__main__'
            
            return frame_module if frame_module else 'UNKNOWN_CALLER'
        
        return 'NO_CALLER_FOUND'
            
    except Exception:
        return 'INSPECT_ERROR'
    finally:
        # Ensure frame objects are garbage collected
        if 'frame' in locals():
            del frame
        if 'current_frame' in locals():
            del current_frame

# ===========================
# Singleton Instance
# ===========================
log = _Logger()
"""The singleton logger instance for application-wide use."""
