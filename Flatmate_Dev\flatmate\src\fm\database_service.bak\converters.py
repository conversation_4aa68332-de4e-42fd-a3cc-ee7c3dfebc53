"""
Converters for transforming data between different formats.
"""

import csv
from datetime import datetime
from typing import Any, Dict, List, Optional

from fm.core.services.logger import log
from fm.core.data_services.standards.fm_standard_columns import StandardColumns

from .repository.transaction_repository import Transaction


class CSVToTransactionConverter:
    """
    Converts CSV data to Transaction objects.

    This class bridges the gap between the existing CSV processing in the Update Data
    module and the new database system.
    """

    def __init__(self, date_format: str = "%Y-%m-%d"):
        """
        Initialize the converter.

        Args:
            date_format: Format string for parsing dates
        """
        self.date_format = date_format

    def convert_csv_file(
        self, file_path: str, source_bank: str = "", account_number: str = ""
    ) -> List[Transaction]:
        """
        Convert a CSV file to a list of Transaction objects.

        Args:
            file_path: Path to the CSV file
            source_bank: Name of the bank that produced the CSV
            account_number: Account number for the transactions

        Returns:
            List of Transaction objects
        """
        transactions = []

        with open(file_path, "r", encoding="utf-8-sig") as f:
            reader = csv.DictReader(f)
            for row in reader:
                transaction = self._row_to_transaction(
                    row, source_bank, account_number, file_path
                )
                if transaction:
                    transactions.append(transaction)

        return transactions

    def convert_csv_data(
        self,
        csv_data: List[Dict[str, str]],
        source_bank: str = "",
        account_number: str = "",
        source_file: str = "",
    ) -> List[Transaction]:
        """
        Convert CSV data to a list of Transaction objects.

        Args:
            csv_data: List of dictionaries representing CSV rows
            source_bank: Name of the bank that produced the CSV
            account_number: Account number for the transactions
            source_file: Name of the source file

        Returns:
            List of Transaction objects
        """
        transactions: List[Transaction] = []

        for row in csv_data:
            transaction = self._row_to_transaction(
                row, source_bank, account_number, source_file
            )
            if transaction:
                transactions.append(transaction)

        return transactions

    def _row_to_transaction(
        self,
        row: Dict[str, str],
        source_bank: str,
        account_number: str,
        source_file: str,
    ) -> Optional[Transaction]:
        """
        Convert a CSV row to a Transaction object.

        Args:
            row: Dictionary representing a CSV row
            source_bank: Name of the bank that produced the CSV
            account_number: Account number for the transaction
            source_file: Name of the source file

        Returns:
            Transaction object or None if conversion failed
        """
        # Map CSV fields to Transaction fields based on fm_standard columns
        try:
            # Extract date - use standard column name with fallbacks
            date_str = (
                row.get(StandardColumns.DATE.value)
                or row.get("date")
                or row.get("Transaction Date")
            )
            if not date_str:
                return None

            try:
                date = datetime.strptime(date_str, self.date_format)
            except ValueError:
                # Try alternative formats
                try:
                    date = datetime.strptime(date_str, "%d/%m/%Y")
                except ValueError:
                    try:
                        date = datetime.strptime(date_str, "%m/%d/%Y")
                    except ValueError:
                        # Last resort - try to parse with dateutil
                        from dateutil import parser

                        date = parser.parse(date_str)

            # Extract description - use standard column name with fallbacks
            description = (
                row.get(StandardColumns.DETAILS.value)
                or row.get("description")
                or row.get("Description")
                or row.get("Narrative")
                or ""
            )

            # Extract amount - use standard column name with fallbacks
            # First check if we have a single amount column
            amount_str = row.get(StandardColumns.AMOUNT.value)

            # If not, check for separate debit/credit columns
            if not amount_str:
                debit = row.get(StandardColumns.DEBIT_AMOUNT.value)
                credit = row.get(StandardColumns.CREDIT_AMOUNT.value)

                if debit and debit.strip():
                    # Debit is negative
                    amount_str = f"-{debit}"
                elif credit and credit.strip():
                    amount_str = credit
                else:
                    # Fall back to non-standard column names
                    amount_str = (
                        row.get("amount")
                        or row.get("Amount")
                        or row.get("Value")
                        or row.get("Transaction Amount")
                        or "0"
                    )

            # Handle different amount formats
            # Check if amount_str is already a float
            if isinstance(amount_str, (float, int)):
                amount = float(amount_str)
            else:
                # Handle string formatting
                try:
                    # Remove commas
                    amount_str = amount_str.replace(",", "")
                    amount = float(amount_str)
                except (ValueError, AttributeError):
                    # Try handling currency symbols
                    try:
                        # Strip non-numeric characters except decimal point and minus
                        amount_str = "".join(
                            c for c in amount_str if c.isdigit() or c in ".-"
                        )
                        amount = float(amount_str)
                    except (ValueError, AttributeError):
                        # If all else fails, log the error and return 0
                        log(f"Could not convert amount: {amount_str}", level="warning")
                        amount = 0.0
                # The amount has already been converted in the previous try/except blocks

            # Extract transaction type - use standard column name with fallbacks
            transaction_type = (
                row.get(StandardColumns.PAYMENT_TYPE.value)
                or row.get("type")
                or row.get("Type")
                or row.get("Transaction Type")
                or ""
            )

            # Get account number - use provided account number or look for it in the data
            actual_account = account_number
            if not actual_account and StandardColumns.ACCOUNT.value in row:
                actual_account = row.get(StandardColumns.ACCOUNT.value) or ""

            # Extract balance if available
            balance_str = row.get(StandardColumns.BALANCE.value)
            balance = None
            if balance_str:
                try:
                    balance = float(balance_str.replace(",", ""))
                except (ValueError, AttributeError):
                    pass

            # Extract additional reference fields
            tp_ref = row.get(StandardColumns.TP_REF.value, "")
            tp_part = row.get(StandardColumns.TP_PART.value, "")
            tp_code = row.get(StandardColumns.TP_CODE.value, "")
            op_ref = row.get(StandardColumns.OP_REF.value, "")
            op_part = row.get(StandardColumns.OP_PART.value, "")
            op_code = row.get(StandardColumns.OP_CODE.value, "")
            op_name = row.get(StandardColumns.OP_NAME.value, "")
            op_account = row.get(StandardColumns.OP_ACCOUNT.value, "")

            # Get original source filename if available
            original_source = row.get(
                StandardColumns.SOURCE_FILENAME.value, source_file
            )

            # Combine reference fields into notes for now
            # This preserves the data until we expand the Transaction model
            notes = {
                "balance": balance,
                "tp_ref": tp_ref,
                "tp_part": tp_part,
                "tp_code": tp_code,
                "op_ref": op_ref,
                "op_part": op_part,
                "op_code": op_code,
                "op_name": op_name,
                "op_account": op_account,
            }

            # Create Transaction object
            return Transaction(
                date=date,
                description=description,
                amount=amount,
                account_number=actual_account,
                transaction_type=transaction_type,
                source_bank=source_bank,
                source_file=original_source,  # Use original source file
                import_date=datetime.now(),
                notes=str(notes),  # Store additional fields as JSON in notes
            )

        except Exception as e:
            # Log the error but don't crash
            print(f"Error converting row to transaction: {e}")
            return None


class TransactionToCSVConverter:
    """
    Converts Transaction objects to CSV format.

    This allows exporting data from the database back to CSV format.
    """

    def convert_to_csv(
        self,
        transactions: List[Transaction],
        output_file: str,
        include_headers: bool = True,
    ) -> bool:
        """
        Convert transactions to a CSV file.

        Args:
            transactions: List of Transaction objects
            output_file: Path to the output CSV file
            include_headers: Whether to include headers in the CSV

        Returns:
            True if conversion was successful
        """
        if not transactions:
            return False

        try:
            with open(output_file, "w", newline="", encoding="utf-8") as f:
                fieldnames = [
                    "date",
                    "description",
                    "amount",
                    "account_number",
                    "transaction_type",
                    "category",
                    "notes",
                    "tags",
                ]

                writer = csv.DictWriter(f, fieldnames=fieldnames)

                if include_headers:
                    writer.writeheader()

                for transaction in transactions:
                    writer.writerow(
                        {
                            "date": (
                                transaction.date.strftime("%Y-%m-%d")
                                if transaction.date
                                else ""
                            ),
                            "description": transaction.description,
                            "amount": transaction.amount,
                            "account_number": transaction.account_number,
                            "transaction_type": transaction.transaction_type,
                            "category": transaction.category,
                            "notes": transaction.notes,
                            "tags": transaction.tags,
                        }
                    )

            return True

        except Exception as e:
            print(f"Error converting transactions to CSV: {e}")
            return False
