"""Data service for accessing the database."""

from datetime import datetime
from typing import List, Optional, Dict, Any

import pandas as pd

from typing import Dict, Any
import pandas as pd

from .repository.sqlite_repository import SQLiteTransactionRepository
from .repository.transaction_repository import Transaction, ImportResult


class DataService:
    """Service for accessing and manipulating data in the database."""
    
    def __init__(self):
        """Initialize the data service."""
        self.transaction_repo = SQLiteTransactionRepository()
    
    def get_transactions(
        self, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        account_number: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        description_contains: Optional[str] = None,
        tags_contain: Optional[str] = None
    ) -> List[Transaction]:
        """
        Get transactions with optional filtering.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            account_number: Optional account number filter
            min_amount: Optional minimum amount filter
            max_amount: Optional maximum amount filter
            description_contains: Optional description text filter
            tags_contain: Optional tags text filter
            
        Returns:
            List of Transaction objects matching the filters
        """
        # Build filter dictionary
        filters = {}
        
        if start_date:
            filters['start_date'] = start_date
        
        if end_date:
            filters['end_date'] = end_date
        
        if account_number:
            filters['account_number'] = account_number
        
        # Get transactions from repository
        return self.transaction_repo.get_transactions(filters)
    
    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            tags: New tags string (comma-separated)
            
        Returns:
            True if successful, False otherwise
        """
        return self.transaction_repo.update_transaction(
            transaction_id, {'tags': tags}
        )
    
    def get_unique_account_numbers(self) -> List[str]:
        """
        Get a list of unique account numbers in the database.
        
        Returns:
            List of unique account numbers
        """
        # Get all transactions
        transactions = self.transaction_repo.get_transactions()
        
        # Extract unique account numbers
        account_numbers = set()
        for transaction in transactions:
            if transaction.account_number:
                account_numbers.add(transaction.account_number)
        
        return self.get_transactions(filters)
        
    def import_transactions_from_df(self, df: pd.DataFrame, source_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Import transactions from a pandas DataFrame into the database.
        
        Args:
            df: DataFrame containing transaction data with proper column names
            source_file: Optional source file path for reference
            
        Returns:
            Dictionary with import results containing:
            - added_count: Number of new transactions added
            - duplicate_count: Number of duplicate transactions skipped
            - error_count: Number of errors encountered
            - errors: List of error messages if any
        """
        try:
            if df is None or df.empty:
                return {
                    'added_count': 0,
                    'duplicate_count': 0,
                    'error_count': 0,
                    'errors': ['No data to import']
                }
                
            # Delegate to the repository's add_transactions_from_df method
            result = self.transaction_repo.add_transactions_from_df(df, source_file=source_file)
            
            # Return the result as a dictionary
            return {
                'added_count': result.added_count,
                'duplicate_count': result.duplicate_count,
                'error_count': result.error_count,
                'errors': result.errors
            }
            
        except Exception as e:
            error_msg = f"Error importing transactions: {str(e)}"
            return {
                'added_count': 0,
                'duplicate_count': 0,
                'error_count': len(df) if df is not None else 1,
                'errors': [error_msg]
            }
