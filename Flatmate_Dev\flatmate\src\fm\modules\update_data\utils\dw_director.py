#!/usr/bin/env python3
"""
Director module for coordinating the data processing pipeline.
"""


import datetime
import os
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from fm.core.services.logger import log

from ..config.ud_config import ud_config
from ..services.events import UpdateDataEventService

from .dw_pipeline import (
    back_up_originals,
    handle_unrecognized_files,
    merge_dataframes,
    move_to_unrecognised,
    save_master_file,
    update_database_from_df,

    validate_final_data,
)
from .statement_handlers._handler_registry import get_handler
from .processing_tracker import (
    clear_all_trackers,
    processed_files_tracker,
    unrecognised_files_tracker,
)


# Custom exceptions
class FileBackupError(Exception):
    """Exception raised when file backup fails."""

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details


class DatabaseUpdateError(Exception):
    """Exception raised when database update fails."""

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details


def dw_director(job_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Orchestrates the bank statement processing pipeline.

    Args:
        job_sheet: Dictionary containing filepaths, save_folder, and update_database flag

    Returns:
        Dictionary with processing results and statistics
    """
    clear_all_trackers()
    filepaths = job_sheet["filepaths"]
    save_folder = job_sheet["save_folder"]
    update_database_flag = job_sheet.get("update_database", False)
    cleanup_source_folder = job_sheet.get("cleanup_source_folder", True)

    UpdateDataEventService.publish_processing_started(job_sheet)

    try:
        # Step 1: Load and process files from the given filepaths.
        formatted_dfs, processed_files, unrecognized_files = _load_and_process_files(filepaths)

        # If no files were processed, return an early error response.
        if not formatted_dfs:
            return _build_error_response(
                "No files could be processed",
                filepaths,
                unrecognized_files=unrecognized_files
            )

        # Merge all processed dataframes into a single master dataframe
        merged_df, stats = merge_dataframes(formatted_dfs)
        stats["processed_files"] = list(processed_files)

        # Step 3: Handle any files that were not recognized by the handlers.
        unrecognized_count = 0
        if cleanup_source_folder and unrecognized_files:
            # The pipeline now handles moving and logging unrecognized files.
            # The director remains responsible for tracking them.
            for item in unrecognized_files:
                if item["filepath"] not in processed_files:
                    unrecognised_files_tracker.add(item["filepath"])

            unrecognized_count = handle_unrecognized_files(
                unrecognized_files, processed_files
            )

        stats["unrecognized_files"] = unrecognized_count

        # If the merged dataframe is empty, no further action is needed.
        if merged_df.empty:
            return _build_error_response(
                "No data to process after merging",
                filepaths,
                list(processed_files),
                unrecognized_files
            )

        # Step 4: Backup original files and clean up.
        backup_status, backup_result = back_up_originals(list(processed_files), save_folder, cleanup_source_files=cleanup_source_folder)
        if not backup_status:
            raise FileBackupError(str(backup_result), details=backup_result)

        backup_msg = backup_result.get("message", "")
        backup_stats = backup_result if isinstance(backup_result, dict) else {}

        # Step 5: Final data validation
        validation_passed, validation_errors = validate_final_data(merged_df)
        if not validation_passed:
            return _build_error_response(
                "Final data validation failed",
                filepaths,
                list(processed_files),
                unrecognized_files,
                details={"validation_errors": validation_errors, "backup_info": backup_stats},
            )

        # Step 6: Save the master file.
        save_result = save_master_file(merged_df, save_folder)
        output_path = save_result.get("output_path")

        if not save_result.get("save_success"):
            return _build_error_response(
                "Error saving master file",
                filepaths,
                list(processed_files),
                unrecognized_files,
                details={"save_error": save_result.get("error"), "backup_info": backup_stats},
            )

        if output_path:
            ud_config.update_master_location(output_path)

        # Step 7: Update the database if requested.
        database_update_info = {}
        if update_database_flag:
            import_result = update_database_from_df(merged_df, output_path)
            if import_result:
                if import_result.added_count > 0 or import_result.duplicate_count > 0 or import_result.error_count > 0:
                    log.info(
                        f"Database updated from {output_path}: "
                        f"{import_result.added_count} new, "
                        f"{import_result.duplicate_count} duplicates, "
                        f"{import_result.error_count} errors."
                    )
                    database_update_info = {
                        "database_updated": True,
                        "added_count": import_result.added_count,
                        "duplicate_count": import_result.duplicate_count,
                        "error_count": import_result.error_count,
                    }
                else:
                    log.info(f"No new records to add from {output_path}.")

        # Step 8: Build and return the final success response.
        return _build_success_response(
            filepaths=filepaths,
            processed_files=list(processed_files),
            unrecognized_files=unrecognized_files,
            stats=stats,
            merged_df=merged_df,
            backup_msg=backup_msg,
            backup_stats=backup_stats,
            database_update_info=database_update_info,
            output_path=output_path,
        )

    except (FileBackupError, DatabaseUpdateError) as e:
        return _build_error_response(
            str(e), filepaths, list(processed_files_tracker.get()), list(unrecognised_files_tracker.get()), details=e.details
        )

    except Exception as e:
        log.error(f"An unexpected error occurred in the director: {e}", exc_info=True)
        return _build_error_response(
            f"An unexpected error occurred: {e}",
            filepaths,
            list(processed_files_tracker.get()),
            list(unrecognised_files_tracker.get()),
            details={"error_type": e.__class__.__name__},
        )


# --- Helper Functions ---

def _load_and_process_files(
    filepaths: List[str],
) -> Tuple[List[pd.DataFrame], set, List[Dict]]:
    """
    Load and process files by finding a suitable handler and delegating the processing.
    """
    formatted_dfs = []
    processed_files = set()
    unrecognized_files = []

    for path in filepaths:
        filename = os.path.basename(path)
        log.info(f"Processing file: {filename}")

        # Step 1: Find a handler that can process the file.
        handler = get_handler(path)

        if not handler:
            log.warning(f"Skipping unrecognized file (no handler found): {filename}")
            unrecognized_files.append(
                {"filepath": path, "reason": "No suitable handler found."}
            )
            unrecognised_files_tracker.add(path)
            continue

        # Step 2: Process the file using the matched handler.
        # The handler is now responsible for reading and formatting the file.
        try:
            processed_df = handler.process_file(path)

            if processed_df is not None and not processed_df.empty:
                formatted_dfs.append(processed_df)
                processed_files.add(path)
                processed_files_tracker.add(path)
                log.info(f"Successfully processed: {filename} with {handler.__class__.__name__}")
            else:
                # This case can be triggered if the file is empty or processing fails validation.
                # The handler's internal logging should provide more details.
                log.warning(f"Skipping file (processing returned empty result): {filename}")
                unrecognized_files.append(
                    {"filepath": path, "reason": "Processing returned no data."}
                )
                unrecognised_files_tracker.add(path)

        except Exception as e:
            # This catches unexpected errors from the handler's process_file method.
            log.error(f"Error processing file {filename} with {handler.__class__.__name__}: {e}")
            unrecognized_files.append(
                {"filepath": path, "reason": f"Processing error: {e}"}
            )
            unrecognised_files_tracker.add(path)

    return formatted_dfs, processed_files, unrecognized_files





def _build_success_response(
    filepaths: List[str],
    processed_files: List[str],
    unrecognized_files: List[Dict],
    stats: Dict,
    merged_df: pd.DataFrame,
    backup_msg: str,
    backup_stats: Dict,
    database_update_info: Dict,
    output_path: str,
) -> Dict:
    """Construct the final success response dictionary."""
    response = {
        "status": "success",
        "message": "Processing completed successfully",
        "details": {
            "input_files": len(filepaths),
            "processed_files": len(processed_files),
            "unrecognized_files": len(unrecognized_files),
            "duplicates_removed": stats.get("duplicates_removed", 0),
            "output_rows": len(merged_df),
            "backup_message": backup_msg,
            "backup_files_count": backup_stats.get("backed_up_count", 0),
            "backup_skipped_count": backup_stats.get("skipped_count", 0),
            "unrecognized_details": [f for f in unrecognized_files if f['filepath'] not in processed_files],
            **database_update_info,
        },
        "output_path": output_path,
    }
    UpdateDataEventService.publish_processing_completed(
        {
            "stats": stats,
            "processed_files": processed_files,
            "backup_stats": backup_stats,
        }
    )
    return response


def _build_error_response(
    message: str,
    filepaths: List[str],
    processed_files: Optional[List[str]] = None,
    unrecognized_files: Optional[List[Dict]] = None,
    details: Optional[Dict] = None,
) -> Dict:
    """Construct a standardized error response."""
    processed_files = processed_files or []
    unrecognized_files = unrecognized_files or []
    return {
        "status": "error",
        "message": message,
        "details": {
            "error_details": details or {},
            "input_files": len(filepaths),
            "processed_files": len(processed_files),
            "unrecognized_files": len(unrecognized_files),
            "unrecognized_details": [f for f in unrecognized_files if f['filepath'] not in processed_files],
        },
    }


# Database functions have been moved to dw_pipeline.py


if __name__ == "__main__":
    pass
