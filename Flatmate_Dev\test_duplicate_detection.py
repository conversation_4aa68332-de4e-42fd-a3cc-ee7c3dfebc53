#!/usr/bin/env python3
"""
Test script to verify the new duplicate detection logic works correctly.
"""

import sys
import os
sys.path.insert(0, 'flatmate/src')

import pandas as pd
from fm.core.data_services.standards.columns import Columns
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.data_services.db_io_service import DBIOService

def test_duplicate_detection():
    """Test the new SOURCE_UID and DB_UID duplicate detection logic."""
    
    print("Testing new duplicate detection logic...")
    
    # Create test data
    test_data = {
        Columns.DATE.db_name: ['2024-01-01', '2024-01-02', '2024-01-01'],
        Columns.DETAILS.db_name: ['Test Transaction 1', 'Test Transaction 2', 'Test Transaction 1'],
        Columns.AMOUNT.db_name: [100.50, 200.75, 100.50],
        Columns.ACCOUNT.db_name: ['12-3456-7890123-00', '12-3456-7890123-00', '12-3456-7890123-00'],
        Columns.BALANCE.db_name: [1000.50, 1201.25, 1000.50],
        Columns.SOURCE_UID.db_name: ['BANK_ID_001', 'BANK_ID_002', 'BANK_ID_001']  # First and third are duplicates
    }
    
    df = pd.DataFrame(test_data)
    print(f"Created test DataFrame with {len(df)} transactions")
    print(df)
    
    # Test with temporary database
    print("Creating repository...")
    repo = SQLiteTransactionRepository(db_path=":memory:")
    print("Repository created successfully")
    db_service = DBIOService(repo=repo)
    
    # First import
    print("\n--- First Import ---")
    result1 = db_service.update_database(df)
    print(f"Added: {result1.added_count}, Duplicates: {result1.duplicate_count}")
    
    # Second import (should detect duplicates)
    print("\n--- Second Import (should detect duplicates) ---")
    result2 = db_service.update_database(df)
    print(f"Added: {result2.added_count}, Duplicates: {result2.duplicate_count}")
    
    # Test with data that has no SOURCE_UID (should use DB_UID)
    print("\n--- Test without SOURCE_UID (DB_UID fallback) ---")
    test_data_no_source = {
        Columns.DATE.db_name: ['2024-01-03', '2024-01-03'],
        Columns.DETAILS.db_name: ['No Source UID 1', 'No Source UID 1'],
        Columns.AMOUNT.db_name: [50.25, 50.25],
        Columns.ACCOUNT.db_name: ['12-3456-7890123-00', '12-3456-7890123-00'],
        Columns.BALANCE.db_name: [1251.50, 1251.50],
        # No SOURCE_UID - should use DB_UID for duplicate detection
    }
    
    df_no_source = pd.DataFrame(test_data_no_source)
    result3 = db_service.update_database(df_no_source)
    print(f"Added: {result3.added_count}, Duplicates: {result3.duplicate_count}")
    
    # Import again (should detect duplicate via DB_UID)
    result4 = db_service.update_database(df_no_source)
    print(f"Second import - Added: {result4.added_count}, Duplicates: {result4.duplicate_count}")
    
    print("\n--- Test Results ---")
    print("✓ SOURCE_UID duplicate detection working" if result2.duplicate_count == 3 else "✗ SOURCE_UID duplicate detection failed")
    print("✓ DB_UID duplicate detection working" if result4.duplicate_count == 2 else "✗ DB_UID duplicate detection failed")
    
    return True

if __name__ == "__main__":
    try:
        test_duplicate_detection()
        print("\n✓ All tests completed successfully!")
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
