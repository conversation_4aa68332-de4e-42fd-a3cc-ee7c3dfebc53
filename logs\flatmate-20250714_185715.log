2025-07-14 18:57:15 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-14 18:57:16 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-14 18:57:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 18:57:17 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-14 18:57:17 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-14 18:57:17 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-14 18:57:17 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-14 18:57:17 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-14 18:57:17 - [fm.main] [INFO] - Application starting...
2025-07-14 18:57:17 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-14 18:57:17 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-14 18:57:17 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'update_data', 'categorize']
2025-07-14 18:57:17 - [fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-14 18:57:17 - [fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-14 18:57:17 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-14 18:57:17 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-14 18:57:19 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-14 18:57:19 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-14 18:57:19 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-14 18:57:19 - [fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-14 18:57:19 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-14 18:57:47 - [fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-14 18:57:47 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-14 18:57:47 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-14 18:57:47 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-14 18:57:47 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-14 18:57:47 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-14 18:57:47 - [fm.modules.update_data.ud_presenter] [DEBUG] - ud_presenter called self.view.setup_in_main_window
2025-07-14 19:16:10 - [fm.modules.update_data.ud_presenter] [DEBUG] - Source selection requested for type: Select individual files...
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:18 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.ud_presenter] [DEBUG] - Processing job sheet: {'filepaths': ['C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-00_13Jun.CSV', 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-01_13Jun.CSV', 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-04_13Jun.CSV'], 'save_folder': 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025', 'update_database': True}
2025-07-14 19:16:27 - [fm.modules.update_data.ud_presenter] [INFO] - Processing started for 3 files
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-00 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-00_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-01 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-01_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-04 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-04_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Deduplicating based on columns: ['Date', 'Details', 'Amount', 'Balance', 'Unique Id']
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Removed 7 duplicate transactions.
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Found 4 suspicious transactions with non-zero amounts but no balance change
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 489):
  Account: 38-9004-0646977-00
  Date: 2024-09-27 00:00:00
  Amount: -1.0
  Balance: 0.07 (Prev: 0.07)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 686):
  Account: 38-9004-0646977-00
  Date: 2025-04-03 00:00:00
  Amount: -500.0
  Balance: 500.02 (Prev: 500.02)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 01 ;Rent and Bills
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 697):
  Account: 38-9004-0646977-00
  Date: 2025-04-15 00:00:00
  Amount: -20.0
  Balance: 326.02 (Prev: 326.02)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1093):
  Account: 38-9004-0646977-04
  Date: 2023-12-10 00:00:00
  Amount: -7.39
  Balance: 45.75 (Prev: 45.75)
  Details: POS W/D FOUR SQUARE H-10:22 ;
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Successfully saved master file to C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025\fmMaster_20250714_191627.csv
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Master file created successfully at: C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025\fmMaster_20250714_191627.csv
2025-07-14 19:16:27 - [fm.modules.update_data.utils.dw_director] [INFO] - Updating database with new transactions...
2025-07-14 19:16:27 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-14 19:16:28 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Database update complete: 0 added, 2099 duplicates, 0 errors.
2025-07-14 19:16:28 - [fm.modules.update_data.utils.dw_director] [INFO] - Skipped 2099 duplicate transactions
2025-07-14 19:16:28 - [fm.modules.update_data.ud_presenter] [INFO] - File processing completed
