2025-07-14 19:36:19 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-14 19:36:20 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-14 19:36:21 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:21 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-14 19:36:21 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-14 19:36:21 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-14 19:36:21 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-14 19:36:21 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-14 19:36:21 - [fm.main] [INFO] - Application starting...
2025-07-14 19:36:21 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-14 19:36:21 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-14 19:36:21 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'update_data', 'categorize']
2025-07-14 19:36:21 - [fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-14 19:36:21 - [fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-14 19:36:21 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-14 19:36:21 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-14 19:36:23 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-14 19:36:23 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-14 19:36:23 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-14 19:36:23 - [fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-14 19:36:23 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-14 19:36:26 - [fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-14 19:36:26 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-14 19:36:26 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-14 19:36:26 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-14 19:36:26 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-14 19:36:26 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-14 19:36:26 - [fm.modules.update_data.ud_presenter] [DEBUG] - ud_presenter called self.view.setup_in_main_window
2025-07-14 19:36:31 - [fm.modules.update_data.ud_presenter] [DEBUG] - Source selection requested for type: Select individual files...
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:38 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.ud_presenter] [DEBUG] - Processing job sheet: {'filepaths': ['C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-00_13Jun.CSV', 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-01_13Jun.CSV', 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/38-9004-0646977-04_13Jun.CSV'], 'save_folder': 'C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025', 'update_database': True}
2025-07-14 19:36:42 - [fm.modules.update_data.ud_presenter] [INFO] - Processing started for 3 files
2025-07-14 19:36:42 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:42 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-00_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-00 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:36:42 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-00_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:42 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-01_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-01 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:36:42 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-01_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:36:42 - [fm.modules.update_data.utils.dw_director] [INFO] - Processing file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-07-14 19:36:42 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-07-14 19:36:42 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankBasicCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 0 (Account: True, Cols: False, NumCols: False) -> Fail
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [DEBUG] - Handler KiwibankFullCSVHandler score for '38-9004-0646977-04_13Jun.CSV': 20 (Account + Columns)
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._handler_registry] [DEBUG] - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-07-14 19:36:43 - [fm.modules.update_data.utils.statement_handlers._base_statement_handler] [INFO] - account_number 38-9004-0646977-04 extracted from AccountNumberSource.FROM_DATA
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_director] [INFO] - Successfully processed: 38-9004-0646977-04_13Jun.CSV with KiwibankFullCSVHandler
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Deduplicating based on columns: ['Date', 'Details', 'Amount', 'Balance', 'Unique Id']
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Removed 7 duplicate transactions.
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Found 60 suspicious transactions with non-zero amounts but no balance change
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 66):
  Account: 38-9004-0646977-00
  Date: 2023-09-27 00:00:00
  Amount: -195.0
  Balance: 0.3 (Prev: 195.3)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 67):
  Account: 38-9004-0646977-00
  Date: 2023-09-30 00:00:00
  Amount: 0.0
  Balance: 0.3 (Prev: 0.3)
  Details: POSREJ Insuff. Funds-11:53 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 128):
  Account: 38-9004-0646977-00
  Date: 2023-12-22 00:00:00
  Amount: -440.0
  Balance: 2.36 (Prev: 4.62)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 129):
  Account: 38-9004-0646977-00
  Date: 2023-12-27 00:00:00
  Amount: 0.0
  Balance: 2.36 (Prev: 2.36)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 235):
  Account: 38-9004-0646977-00
  Date: 2024-04-14 00:00:00
  Amount: -23.0
  Balance: 0.31 (Prev: 23.31)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 236):
  Account: 38-9004-0646977-00
  Date: 2024-04-14 00:00:00
  Amount: 0.0
  Balance: 0.31 (Prev: 0.31)
  Details: POSREJ Insuff. Funds-14:57 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 270):
  Account: 38-9004-0646977-00
  Date: 2024-05-01 00:00:00
  Amount: -10.0
  Balance: 0.44 (Prev: 10.44)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 271):
  Account: 38-9004-0646977-00
  Date: 2024-05-02 00:00:00
  Amount: 0.0
  Balance: 0.44 (Prev: 0.44)
  Details: POSREJ Insuff. Funds-21:52 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 437):
  Account: 38-9004-0646977-00
  Date: 2024-08-21 00:00:00
  Amount: -70.0
  Balance: 9.05 (Prev: 79.05)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 438):
  Account: 38-9004-0646977-00
  Date: 2024-08-21 00:00:00
  Amount: 0.0
  Balance: 9.05 (Prev: 9.05)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 439):
  Account: 38-9004-0646977-00
  Date: 2024-08-24 00:00:00
  Amount: 0.0
  Balance: 9.05 (Prev: 0.05)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 440):
  Account: 38-9004-0646977-00
  Date: 2024-08-24 00:00:00
  Amount: 0.0
  Balance: 9.05 (Prev: 9.05)
  Details: For $20.00 to WgtnCityCouncil 23 AUG 24 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 442):
  Account: 38-9004-0646977-00
  Date: 2024-08-27 00:00:00
  Amount: -0.05
  Balance: 0.0 (Prev: 9.05)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 443):
  Account: 38-9004-0646977-00
  Date: 2024-08-28 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 445):
  Account: 38-9004-0646977-00
  Date: 2024-08-31 00:00:00
  Amount: -0.01
  Balance: -2.51 (Prev: -2.5)
  Details: INTEREST DEBIT ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 446):
  Account: 38-9004-0646977-00
  Date: 2024-09-04 00:00:00
  Amount: 0.0
  Balance: -2.51 (Prev: -2.51)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 447):
  Account: 38-9004-0646977-00
  Date: 2024-09-04 00:00:00
  Amount: 0.0
  Balance: -2.51 (Prev: -2.51)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 448):
  Account: 38-9004-0646977-00
  Date: 2024-09-04 00:00:00
  Amount: 0.0
  Balance: -2.51 (Prev: -2.51)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 486):
  Account: 38-9004-0646977-00
  Date: 2024-09-24 00:00:00
  Amount: -235.0
  Balance: 13.07 (Prev: 248.07)
  Details: AP#******** TO J M WILLIAMS ;Pay J M WILLIAMS
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 487):
  Account: 38-9004-0646977-00
  Date: 2024-09-26 00:00:00
  Amount: 0.0
  Balance: 13.07 (Prev: 13.07)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 491):
  Account: 38-9004-0646977-00
  Date: 2024-09-27 00:00:00
  Amount: -20.0
  Balance: 0.07 (Prev: 20.07)
  Details: Direct Debit -EZIDEBIT (NZ) LIMITED ;Ref: ********...
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 602):
  Account: 38-9004-0646977-00
  Date: 2024-12-17 00:00:00
  Amount: -108.75
  Balance: 255.77 (Prev: 364.52)
  Details: AP#******** TO SEAVIEW SELF STORAGE ;unit 038 Seav...
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 603):
  Account: 38-9004-0646977-00
  Date: 2024-12-19 00:00:00
  Amount: 0.0
  Balance: 255.77 (Prev: 255.77)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 688):
  Account: 38-9004-0646977-00
  Date: 2025-04-03 00:00:00
  Amount: -300.0
  Balance: 500.02 (Prev: 380.02)
  Details: PAY Newtown Bowling Club ;301Mansfield Rent 300 NB...
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 699):
  Account: 38-9004-0646977-00
  Date: 2025-04-15 00:00:00
  Amount: 75.0
  Balance: 326.02 (Prev: 251.02)
  Details: TRANSFER FROM Q M SHAW-WILLIAMS - 01 ;2 degrees pa...
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 709):
  Account: 38-9004-0646977-00
  Date: 2025-04-24 00:00:00
  Amount: -0.52
  Balance: 0.0 (Prev: 0.52)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 710):
  Account: 38-9004-0646977-00
  Date: 2025-04-26 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: POSREJ Insuff. Funds-11:47 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 711):
  Account: 38-9004-0646977-00
  Date: 2025-04-26 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 725):
  Account: 38-9004-0646977-00
  Date: 2025-05-17 00:00:00
  Amount: -7.99
  Balance: 3.58 (Prev: 11.57)
  Details: POS W/D NEW WORLD NEW-16:04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 726):
  Account: 38-9004-0646977-00
  Date: 2025-05-24 00:00:00
  Amount: 0.0
  Balance: 3.58 (Prev: 3.58)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 727):
  Account: 38-9004-0646977-00
  Date: 2025-05-31 00:00:00
  Amount: 0.0
  Balance: 3.58 (Prev: 3.58)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 728):
  Account: 38-9004-0646977-00
  Date: 2025-06-07 00:00:00
  Amount: 0.0
  Balance: 3.58 (Prev: 3.58)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 738):
  Account: 38-9004-0646977-01
  Date: 2025-02-20 00:00:00
  Amount: -300.0
  Balance: 0.0 (Prev: 300.0)
  Details: PAY Newtown Bowling Club ;301Mansfield Rent 300 NB...
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 739):
  Account: 38-9004-0646977-01
  Date: 2025-03-21 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 740):
  Account: 38-9004-0646977-01
  Date: 2025-03-21 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: For $58.57 to Flick Energy Limited 20 MAR 25 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 757):
  Account: 38-9004-0646977-01
  Date: 2025-04-24 00:00:00
  Amount: -0.97
  Balance: 0.0 (Prev: 0.97)
  Details: TRANSFER TO Q M SHAW-WILLIAMS - 04 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 758):
  Account: 38-9004-0646977-01
  Date: 2025-04-25 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 759):
  Account: 38-9004-0646977-01
  Date: 2025-04-25 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: For $95.34 to Flick Energy Limited 24 APR 25 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 761):
  Account: 38-9004-0646977-01
  Date: 2025-04-30 00:00:00
  Amount: -0.01
  Balance: 0.0 (Prev: 0.01)
  Details: INTEREST DEBIT ;IRD WITHHOLDING TAX
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 762):
  Account: 38-9004-0646977-01
  Date: 2025-05-23 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 763):
  Account: 38-9004-0646977-01
  Date: 2025-05-23 00:00:00
  Amount: 0.0
  Balance: 0.0 (Prev: 0.0)
  Details: For $110.99 to Flick Energy Limited 22 MAY 25 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1254):
  Account: 38-9004-0646977-04
  Date: 2024-02-26 00:00:00
  Amount: -15.99
  Balance: 0.29 (Prev: 16.28)
  Details: POS W/D Super Cheap A-10:51 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1255):
  Account: 38-9004-0646977-04
  Date: 2024-02-27 00:00:00
  Amount: 0.0
  Balance: 0.29 (Prev: 0.29)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1256):
  Account: 38-9004-0646977-04
  Date: 2024-02-27 00:00:00
  Amount: 0.0
  Balance: 0.29 (Prev: 0.29)
  Details: For $79.21 to LANTERN INSURANCE 26 FEB 24 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1275):
  Account: 38-9004-0646977-04
  Date: 2024-03-13 00:00:00
  Amount: -16.99
  Balance: 19.07 (Prev: 36.06)
  Details: Google Spotify Auckland ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1276):
  Account: 38-9004-0646977-04
  Date: 2024-03-15 00:00:00
  Amount: 0.0
  Balance: 19.07 (Prev: 19.07)
  Details: POSREJ Insuff. Funds-18:42 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1277):
  Account: 38-9004-0646977-04
  Date: 2024-03-15 00:00:00
  Amount: 0.0
  Balance: 19.07 (Prev: 19.07)
  Details: POSREJ Insuff. Funds-19:06 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1278):
  Account: 38-9004-0646977-04
  Date: 2024-03-18 00:00:00
  Amount: 0.0
  Balance: 19.07 (Prev: 19.07)
  Details: POSREJ Insuff. Funds-20:00 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1299):
  Account: 38-9004-0646977-04
  Date: 2024-03-26 00:00:00
  Amount: 100.0
  Balance: 125.59 (Prev: 25.59)
  Details: TRANSFER FROM Q M SHAW-WILLIAMS - 00 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1300):
  Account: 38-9004-0646977-04
  Date: 2024-03-26 00:00:00
  Amount: 0.0
  Balance: 125.59 (Prev: 125.59)
  Details: DD DISHONOUR ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1301):
  Account: 38-9004-0646977-04
  Date: 2024-03-26 00:00:00
  Amount: 0.0
  Balance: 125.59 (Prev: 125.59)
  Details: For $79.21 to LANTERN INSURANCE 25 MAR 24 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1341):
  Account: 38-9004-0646977-04
  Date: 2024-04-14 00:00:00
  Amount: -6.99
  Balance: 9.2 (Prev: 16.19)
  Details: NEW WORLD ISLAND BAY WELLINGTON ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1342):
  Account: 38-9004-0646977-04
  Date: 2024-04-14 00:00:00
  Amount: 0.0
  Balance: 9.2 (Prev: 9.2)
  Details: POSREJ Insuff. Funds-14:25 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1397):
  Account: 38-9004-0646977-04
  Date: 2024-05-02 00:00:00
  Amount: -35.0
  Balance: 23.89 (Prev: 87.89)
  Details: MARY POTTER HOSPICE SHOMARY POTTER H ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1398):
  Account: 38-9004-0646977-04
  Date: 2024-05-02 00:00:00
  Amount: 0.0
  Balance: 23.89 (Prev: 23.89)
  Details: POSREJ Insuff. Funds-21:52 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1660):
  Account: 38-9004-0646977-04
  Date: 2024-08-14 00:00:00
  Amount: -17.0
  Balance: 56.29 (Prev: 73.29)
  Details: POS W/D BURGER KING B-15:21 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1661):
  Account: 38-9004-0646977-04
  Date: 2024-08-15 00:00:00
  Amount: 0.0
  Balance: 56.29 (Prev: 56.29)
  Details: POSREJ Insuff. Funds-19:51 ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1671):
  Account: 38-9004-0646977-04
  Date: 2024-08-17 00:00:00
  Amount: -10.75
  Balance: 8.69 (Prev: 19.44)
  Details: BP 2GO RAILWAY AVENUE LOWER HUTT ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1672):
  Account: 38-9004-0646977-04
  Date: 2024-08-19 00:00:00
  Amount: 0.0
  Balance: 8.69 (Prev: 8.69)
  Details: AP DISHONOUR #******** ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [WARNING] - Suspicious transaction (row 1753):
  Account: 38-9004-0646977-04
  Date: 2024-10-10 00:00:00
  Amount: -16.99
  Balance: 0.29 (Prev: 12.1)
  Details: Google Spotify Auckland ;
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Successfully saved master file to C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025\fmMaster_20250714_193643.csv
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_director] [INFO] - Master file created successfully at: C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025\fmMaster_20250714_193643.csv
2025-07-14 19:36:43 - [fm.modules.update_data.utils.dw_director] [INFO] - Updating database with new transactions...
2025-07-14 19:36:43 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-14 19:36:44 - [fm.modules.update_data.utils.dw_pipeline] [INFO] - Database update complete: 0 added, 2099 duplicates, 0 errors.
2025-07-14 19:36:44 - [fm.modules.update_data.utils.dw_director] [INFO] - Skipped 2099 duplicate transactions
2025-07-14 19:36:44 - [fm.modules.update_data.ud_presenter] [INFO] - File processing completed
