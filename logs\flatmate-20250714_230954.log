2025-07-14 23:09:54 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-14 23:09:54 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: :memory:
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [DEBUG] - Creating index on columns: date, details, amount, account
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 157, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 157, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 157, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:09:54 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 157, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
