2025-07-14 23:12:36 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-14 23:12:36 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-14 23:12:36 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-14 23:12:36 - [fm.core.database.sql_repository.sqlite_repository] [DEBUG] - Creating index on columns: date, details, amount, account
