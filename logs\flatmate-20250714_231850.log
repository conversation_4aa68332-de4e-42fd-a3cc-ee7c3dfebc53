2025-07-14 23:18:50 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-14 23:18:50 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: :memory:
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Creating transactions table...
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Creating table with SQL: CREATE TABLE transactions ("account" TEXT, "amount" REAL, "balance" REAL, "category" TEXT, "credit_amount" REAL, "date" TEXT, "db_uid" TEXT, "debit_amount" REAL, "details" TEXT, "empty" TEXT, "hash" TEXT, "import_date" TEXT, "modified_date" TEXT, "notes" TEXT, "op_account" TEXT, "op_code" TEXT, "op_name" TEXT, "op_part" TEXT, "op_ref" TEXT, "payment_type" TEXT, "source_bank" TEXT, "source_filename" TEXT, "source_type" TEXT, "source_uid" TEXT, "statement_date" TEXT, "tags" TEXT, "tp_code" TEXT, "tp_part" TEXT, "tp_ref" TEXT, "unique_id" TEXT, id INTEGER PRIMARY KEY, is_deleted INTEGER DEFAULT 0)
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Table created successfully
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [DEBUG] - Creating index on columns: date, details, amount, account
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 165, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 165, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 165, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
2025-07-14 23:18:50 - [fm.core.database.sql_repository.sqlite_repository] [ERROR] - Error adding transactions from DataFrame: no such table: transactions
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate/src\fm\core\database\sql_repository\sqlite_repository.py", line 165, in add_transactions_from_df
    cursor.execute(insert_sql)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^
sqlite3.OperationalError: no such table: transactions
