app:
  debug_mode: true
  environment: development
  is_first_run: false
  name: flatMate
  version: 1.0.0
app.is_first_run: false
categorize:
  display:
    amount_decimals: 2
    date_format: yyyy-MM-dd
    description_width: 40
  filters:
    default_days: 30
    remember_last: true
  patterns:
    auto_learn: true
    file_path: ~/.flatmate/data/categorize/patterns.json
  tags:
    auto_suggest: true
    recent_tags: []
categorize.display.amount_decimals: 2
categorize.display.date_format: yyyy-MM-dd
categorize.display.description_width: 40
categorize.filters.default_days: 30
categorize.filters.remember_last: true
categorize.patterns.auto_learn: true
categorize.patterns.file_path: ~/.flatmate/data/categorize/patterns.json
categorize.tags.auto_suggest: true
categorize.tags.recent_tags: []
files:
  config_extensions:
  - .json
  - .yaml
  - .yml
  data_extensions:
  - .csv
  - .xlsx
  - .json
  image_extensions:
  - .png
  - .jpg
  - .jpeg
  max_size: 10485760
  supported_extensions:
  - .csv
  - .xlsx
  - .json
  - .yaml
  - .yml
gui:
  animation:
    duration: 300
    enabled: true
  layout:
    margins: 8
    spacing: 10
  panel:
    opacity: 1.0
    visible: true
  theme:
    font_size: 14
    name: default
  window:
    height: 800
    min_panel_width: 120
    panels:
      left:
        default_width: 240
        last_width: 240
      right:
        default_width: 240
        last_width: 240
    title: flatMate
    width: 1200
gui.layout.margins: 8
gui.layout.spacing: 10
gui.theme.font_size: 14
gui.window.height: 600
gui.window.panels.left.default_width: 240
gui.window.panels.left.last_width: 240
gui.window.panels.right.compact_width: 50
gui.window.panels.right.default_width: 240
gui.window.panels.right.last_width: 240
gui.window.panels.right.state: compact
gui.window.width: 800
last_source_dir: C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025/test_csvs
last_source_option: Select individual files...
logging:
  backup_count: 5
  file_path: ~/.flatmate/logs/app.log
  level: DEBUG
  max_file_size_mb: 10
  show_info: true
  show_warnings: true
paths:
  backup_dir: ~/.flatmate/backups
  base_data_dir: ~/.flatmate
  cache: ~/.flatmate/cache
  config: ~/.flatmate/config
  data: ~/.flatmate/data
  logs: ~/.flatmate/logs
  master: ''
  master_history: []
  recent_files:
  - home
  - update_data
  recent_masters: []
  temp_dir: ~/.flatmate/temp
profiles:
  active_profile: default
  default: {}
recent_masters:
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\originals\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\originals\originals\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\originals\originals\originals\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\test_CSV's\originals\originals\fmMaster.csv
- C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\test_CSV's\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\originals\originals\originals\originals\originals\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\originals\originals\fmMaster.csv
- C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\test_csvs\fmMaster.csv
security:
  data_anonymization: false
  encryption_enabled: true
testing:
  mock_data_enabled: false
  test_mode: false
update_data:
  files:
    allowed_extensions:
    - .csv
    - .xlsx
    - .xls
    ignore_empty: true
    max_size: 100000
  history:
    recent_masters: []
  logging:
    log_processed: true
    max_recent_jobs: 5
  paths:
    data: ~/.flatmate/data/update_data
    temp: ~/.flatmate/temp/update_data
  source:
    default_type: folder
    recent_sources: []
  validation:
    strict_mode: true
update_data.bank.default_type: default
update_data.bank.supported_types:
- default
- asb
- westpac
- anz
update_data.files.allowed_extensions:
- .csv
- .xlsx
- .xls
update_data.files.config_extensions:
- .json
- .yaml
- .yml
update_data.files.data_extensions:
- .csv
- .xlsx
- .xls
update_data.files.ignore_empty: true
update_data.files.image_extensions:
- .png
- .jpg
- .jpeg
update_data.files.max_size: 100000
update_data.files.supported_extensions:
- .csv
- .xlsx
- .xls
- .json
- .yaml
update_data.format.default_type: default
update_data.format.supported_types:
- default
- custom
update_data.formats.bank_type: default
update_data.formats.date_format: '%Y-%m-%d'
update_data.formats.format_type: default
update_data.history.master_history: []
update_data.history.recent_masters:
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_160450.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_160607.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_160656.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_161013.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_182309.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_212932.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_231748.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_232156.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_232203.csv
- c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\output\pipeline_test_output\fmMaster_20250711_232305.csv
update_data.logging.log_processed: true
update_data.logging.max_recent_jobs: 5
update_data.paths.backup: ~/.flatmate/data/update_data/backup
update_data.paths.data: ~/.flatmate/data/update_data
update_data.paths.last_save_dir: ~/.flatmate/data/update_data/last_save
update_data.paths.last_source_dir: C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025
update_data.paths.master: ~/.flatmate/data/update_data/master
update_data.paths.temp: ~/.flatmate/temp/update_data
update_data.paths.unrecognised: ~/.flatmate/data/update_data/unrecognized
update_data.paths.unrecognized: ~/.flatmate/data/update_data/unrecognized
update_data.source.default_type: folder
update_data.source.last_source_option: Select individual files...
update_data.source.recent_sources: []
update_data.validation.strict_mode: true
window.recent_files:
- home
- update_data
- categorize
